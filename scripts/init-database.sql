-- Database Initialization Script for Visa Adapter Application
-- This script creates the necessary databases for different environments
-- Run this script as PostgreSQL superuser before starting the application

-- Create development database
CREATE DATABASE visa_dev
    WITH
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

COMMENT ON DATABASE visa_dev IS 'Development database for Visa Adapter application';

-- Create QA database
CREATE DATABASE visa_qa
    WITH
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

COMMENT ON DATABASE visa_qa IS 'QA database for Visa Adapter application';

-- Create production database
CREATE DATABASE visa_prod
    WITH
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

COMMENT ON DATABASE visa_prod IS 'Production database for Visa Adapter application';

-- Create QA user
DO
$do$
BEGIN
   IF NOT EXISTS (
      SELECT FROM pg_catalog.pg_roles
      WHERE  rolname = 'visa_qa_user') THEN

      CREATE ROLE visa_qa_user LOGIN PASSWORD 'visa_qa_pass';
   END IF;
END
$do$;

-- Create production user
DO
$do$
BEGIN
   IF NOT EXISTS (
      SELECT FROM pg_catalog.pg_roles
      WHERE  rolname = 'visa_prod_user') THEN

      CREATE ROLE visa_prod_user LOGIN PASSWORD 'visa_prod_pass';
   END IF;
END
$do$;

-- Grant privileges to QA user
GRANT ALL PRIVILEGES ON DATABASE visa_qa TO visa_qa_user;

-- Grant privileges to production user
GRANT ALL PRIVILEGES ON DATABASE visa_prod TO visa_prod_user;

-- Grant privileges to postgres user for development (already has access)
GRANT ALL PRIVILEGES ON DATABASE visa_dev TO postgres;

-- Display created databases
\l visa_*

-- Display users
\du visa_*

-- Instructions for running this script:
-- 1. Connect to PostgreSQL as superuser: psql -U postgres
-- 2. Run this script: \i /path/to/scripts/init-database.sql
-- 3. Or run directly: psql -U postgres -f scripts/init-database.sql

-- Database Initialization Script for Visa Adaptor
-- Run this script if you need to manually initialize an empty database
-- This script creates the database and sets up the initial Flyway baseline

-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS visa_dev 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Use the database
USE visa_dev;

-- Create Flyway schema history table manually if needed
-- This table tracks which migrations have been applied
CREATE TABLE IF NOT EXISTS flyway_schema_history (
    installed_rank INT NOT NULL,
    version VARCHAR(50),
    description VARCHAR(200) NOT NULL,
    type VARCHAR(20) NOT NULL,
    script VARCHAR(1000) NOT NULL,
    checksum INT,
    installed_by VARCHAR(100) NOT NULL,
    installed_on TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    execution_time INT NOT NULL,
    success BOOLEAN NOT NULL,
    PRIMARY KEY (installed_rank),
    INDEX flyway_schema_history_s_idx (success)
);

-- Insert baseline record if the table is empty
-- This tells Fly<PERSON> that we're starting from version 0
INSERT IGNORE INTO flyway_schema_history (
    installed_rank, 
    version, 
    description, 
    type, 
    script, 
    checksum, 
    installed_by, 
    installed_on, 
    execution_time, 
    success
) VALUES (
    1, 
    '0', 
    '<< Flyway Baseline >>', 
    'BASELINE', 
    '<< Flyway Baseline >>', 
    NULL, 
    'manual', 
    NOW(), 
    0, 
    1
);

-- Show current migration status
SELECT 'Current Flyway Schema History:' as status;
SELECT * FROM flyway_schema_history ORDER BY installed_rank;

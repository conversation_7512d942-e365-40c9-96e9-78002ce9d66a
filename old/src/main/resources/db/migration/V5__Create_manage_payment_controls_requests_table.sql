-- Migration V5: <PERSON>reate ManagePaymentControls requests table for Visa API integration
-- This table tracks ManagePaymentControls API requests with status and retry management

CREATE TABLE manage_payment_controls_requests (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    message_id VARCHAR(100) NOT NULL UNIQUE,
    client_id VARCHAR(100) NOT NULL,
    account_number VARCHAR(50) NOT NULL,
    buyer_id VARCHAR(100) NOT NULL,
    payment_control_details_data LONGTEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    retry_count INT NOT NULL DEFAULT 0,
    failure_reason VARCHAR(1000),
    processed_at TIMESTAMP,
    visa_response_data LONGTEXT,
    visa_response_code VARCHAR(10),
    visa_response_description VARCHAR(500),
    created_by VA<PERSON>HAR(100) NOT NULL DEFAULT 'SYSTEM',
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VARC<PERSON><PERSON>(100) NOT NULL DEFAULT 'SYSTEM',
    updated_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Optimized indexes for ManagePaymentControls requests
CREATE UNIQUE INDEX idx_manage_payment_controls_message_id ON manage_payment_controls_requests(message_id);
CREATE INDEX idx_manage_payment_controls_status ON manage_payment_controls_requests(status);
CREATE INDEX idx_manage_payment_controls_client_id ON manage_payment_controls_requests(client_id);
CREATE INDEX idx_manage_payment_controls_buyer_id ON manage_payment_controls_requests(buyer_id);
CREATE INDEX idx_manage_payment_controls_account_number ON manage_payment_controls_requests(account_number);

-- Composite indexes for common query patterns
CREATE INDEX idx_manage_payment_controls_status_created ON manage_payment_controls_requests(status, created_date);
CREATE INDEX idx_manage_payment_controls_client_status ON manage_payment_controls_requests(client_id, status);
CREATE INDEX idx_manage_payment_controls_retry ON manage_payment_controls_requests(status, retry_count);

-- Comments for documentation
ALTER TABLE manage_payment_controls_requests COMMENT = 'Tracks ManagePaymentControls API requests with status and retry management';
ALTER TABLE manage_payment_controls_requests MODIFY COLUMN message_id VARCHAR(100) COMMENT 'Unique message ID from Visa ManagePaymentControls API';
ALTER TABLE manage_payment_controls_requests MODIFY COLUMN status VARCHAR(20) COMMENT 'Processing status: PENDING, PROCESSING, SUCCESS, RETRY, FAILED';
ALTER TABLE manage_payment_controls_requests MODIFY COLUMN client_id VARCHAR(100) COMMENT 'Client identifier for the request';
ALTER TABLE manage_payment_controls_requests MODIFY COLUMN account_number VARCHAR(50) COMMENT 'Account number for payment control management';
ALTER TABLE manage_payment_controls_requests MODIFY COLUMN buyer_id VARCHAR(100) COMMENT 'Buyer identifier for the request';
ALTER TABLE manage_payment_controls_requests MODIFY COLUMN payment_control_details_data LONGTEXT COMMENT 'JSON serialized payment control details data';
ALTER TABLE manage_payment_controls_requests MODIFY COLUMN retry_count INT COMMENT 'Number of retry attempts for failed requests';
ALTER TABLE manage_payment_controls_requests MODIFY COLUMN visa_response_data LONGTEXT COMMENT 'JSON serialized Visa API response data';
ALTER TABLE manage_payment_controls_requests MODIFY COLUMN visa_response_code VARCHAR(10) COMMENT 'Visa API response code';
ALTER TABLE manage_payment_controls_requests MODIFY COLUMN visa_response_description VARCHAR(500) COMMENT 'Visa API response description';

-- Add check constraint for status enum values
ALTER TABLE manage_payment_controls_requests ADD CONSTRAINT chk_manage_payment_controls_status 
    CHECK (status IN ('PENDING', 'PROCESSING', 'SUCCESS', 'RETRY', 'FAILED'));

-- Add check constraint for retry count to be non-negative
ALTER TABLE manage_payment_controls_requests ADD CONSTRAINT chk_manage_payment_controls_retry_count 
    CHECK (retry_count >= 0);

-- Add check constraint for account number to be non-empty
ALTER TABLE manage_payment_controls_requests ADD CONSTRAINT chk_manage_payment_controls_account_number 
    CHECK (LENGTH(TRIM(account_number)) > 0);

-- Add check constraint for client ID to be non-empty
ALTER TABLE manage_payment_controls_requests ADD CONSTRAINT chk_manage_payment_controls_client_id 
    CHECK (LENGTH(TRIM(client_id)) > 0);

-- Add check constraint for buyer ID to be non-empty
ALTER TABLE manage_payment_controls_requests ADD CONSTRAINT chk_manage_payment_controls_buyer_id 
    CHECK (LENGTH(TRIM(buyer_id)) > 0);

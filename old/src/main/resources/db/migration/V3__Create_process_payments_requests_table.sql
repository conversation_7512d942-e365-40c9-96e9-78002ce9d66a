-- Migration V3: Create ProcessPayments requests table for Visa API integration
-- This table tracks ProcessPayments API requests with status and retry management

CREATE TABLE process_payments_requests (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    message_id VARCHAR(100) NOT NULL UNIQUE,
    action_type INT NOT NULL,
    client_id VARCHAR(100) NOT NULL,
    buyer_id VARCHAR(100) NOT NULL,
    payment_expiry_date VARCHAR(10) NOT NULL,
    account_type INT NOT NULL,
    currency_code VARCHAR(3) NOT NULL,
    payment_gross_amount DECIMAL(19,2) NOT NULL,
    payment_type VARCHAR(10) NOT NULL,
    supplier_name VARCHAR(100) NOT NULL,
    supplier_id BIGINT NOT NULL,
    supplier_vat_number VARCHAR(50),
    supplier_address_line2 VARCHAR(200),
    supplier_address_line1 VARCHAR(100) NOT NULL,
    supplier_city VARCHAR(50) NOT NULL,
    supplier_state VARCHAR(10) NOT NULL,
    supplier_country_code VARCHAR(3) NOT NULL,
    supplier_postal_code VARCHAR(20) NOT NULL,
    primary_email_address VARCHAR(100) NOT NULL,
    email_notes VARCHAR(500),
    invoices_data LONGTEXT NOT NULL,
    alternate_email_addresses_data LONGTEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    retry_count INT NOT NULL DEFAULT 0,
    failure_reason VARCHAR(1000),
    processed_at TIMESTAMP,
    visa_response_data LONGTEXT,
    visa_status_code VARCHAR(10),
    visa_account_number VARCHAR(50),
    created_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM',
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM',
    updated_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Optimized indexes for ProcessPayments requests
CREATE UNIQUE INDEX idx_process_payments_message_id ON process_payments_requests(message_id);
CREATE INDEX idx_process_payments_status ON process_payments_requests(status);
CREATE INDEX idx_process_payments_client_id ON process_payments_requests(client_id);
CREATE INDEX idx_process_payments_buyer_id ON process_payments_requests(buyer_id);

-- Composite indexes for common query patterns
CREATE INDEX idx_process_payments_status_created ON process_payments_requests(status, created_date);
CREATE INDEX idx_process_payments_retry_monitoring ON process_payments_requests(retry_count, status);

-- Comments for documentation
ALTER TABLE process_payments_requests COMMENT = 'Tracks ProcessPayments API requests with status and retry management';
ALTER TABLE process_payments_requests MODIFY COLUMN message_id VARCHAR(100) COMMENT 'Unique message ID from Visa ProcessPayments API';
ALTER TABLE process_payments_requests MODIFY COLUMN status VARCHAR(20) COMMENT 'Processing status: PENDING, PROCESSING, SUCCESS, RETRY, FAILED';
ALTER TABLE process_payments_requests MODIFY COLUMN account_type INT COMMENT 'Account type for payment processing';
ALTER TABLE process_payments_requests MODIFY COLUMN payment_expiry_date VARCHAR(10) COMMENT 'Payment expiry date in YYYY-MM-DD format';
ALTER TABLE process_payments_requests MODIFY COLUMN currency_code VARCHAR(3) COMMENT 'ISO 4217 currency code';
ALTER TABLE process_payments_requests MODIFY COLUMN payment_gross_amount DECIMAL(19,2) COMMENT 'Total payment amount';
ALTER TABLE process_payments_requests MODIFY COLUMN payment_type VARCHAR(10) COMMENT 'Type of payment';
ALTER TABLE process_payments_requests MODIFY COLUMN invoices_data LONGTEXT COMMENT 'JSON serialized invoice data';
ALTER TABLE process_payments_requests MODIFY COLUMN alternate_email_addresses_data LONGTEXT COMMENT 'JSON serialized alternate email addresses';
ALTER TABLE process_payments_requests MODIFY COLUMN retry_count INT COMMENT 'Number of retry attempts for failed requests';
ALTER TABLE process_payments_requests MODIFY COLUMN visa_response_data LONGTEXT COMMENT 'JSON serialized Visa API response data';
ALTER TABLE process_payments_requests MODIFY COLUMN visa_status_code VARCHAR(10) COMMENT 'Visa API response status code';
ALTER TABLE process_payments_requests MODIFY COLUMN visa_account_number VARCHAR(50) COMMENT 'Visa account number from response';

-- Add check constraint for status enum values
ALTER TABLE process_payments_requests ADD CONSTRAINT chk_process_payments_status 
    CHECK (status IN ('PENDING', 'PROCESSING', 'SUCCESS', 'RETRY', 'FAILED'));

-- Add check constraint for retry count to be non-negative
ALTER TABLE process_payments_requests ADD CONSTRAINT chk_process_payments_retry_count 
    CHECK (retry_count >= 0);

-- Add check constraint for action type to be positive
ALTER TABLE process_payments_requests ADD CONSTRAINT chk_process_payments_action_type 
    CHECK (action_type > 0);

-- Migration V4: Create audit table for ProcessPayments requests
-- This migration creates the audit table for ProcessPaymentsRequest entity using Hibernate Envers

-- Audit table for process_payments_requests
CREATE TABLE process_payments_requests_aud (
    id BIGINT NOT NULL,
    rev INT NOT NULL,
    revtype TINYINT,

    -- All fields from original table
    message_id VARCHAR(100),
    action_type INT,
    client_id VARCHAR(100),
    buyer_id VARCHAR(100),
    payment_expiry_date VARCHAR(10),
    account_type INT,
    currency_code VARCHAR(3),
    payment_gross_amount DECIMAL(19,2),
    payment_type VARCHAR(10),
    supplier_name VARCHAR(100),
    supplier_id BIGINT,
    supplier_vat_number VARCHAR(50),
    supplier_address_line2 VARCHAR(200),
    supplier_address_line1 VARCHAR(100),
    supplier_city VARCHAR(50),
    supplier_state VARCHAR(10),
    supplier_country_code VARCHAR(3),
    supplier_postal_code VARCHAR(20),
    primary_email_address VARCHAR(100),
    email_notes VARCHAR(500),
    invoices_data LONGTEXT,
    alternate_email_addresses_data LONGTEXT,
    status VARCHAR(20),
    retry_count INT,
    failure_reason VARCHAR(1000),
    processed_at TIMESTAMP,
    visa_response_data LONGTEXT,
    visa_status_code VARCHAR(10),
    visa_account_number VARCHAR(50),

    -- Audit Fields from BaseEntity
    created_date TIMESTAMP,
    updated_date TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),

    PRIMARY KEY (id, rev),
    FOREIGN KEY (rev) REFERENCES revinfo(rev)
);

-- Create indexes for audit table performance
CREATE INDEX idx_process_payments_aud_rev ON process_payments_requests_aud(rev);
CREATE INDEX idx_process_payments_aud_revtype ON process_payments_requests_aud(revtype);
CREATE INDEX idx_process_payments_aud_message_id ON process_payments_requests_aud(message_id);

-- Add comments for audit table
ALTER TABLE process_payments_requests_aud COMMENT = 'Audit table for process_payments_requests tracking all changes with Hibernate Envers';
ALTER TABLE process_payments_requests_aud MODIFY COLUMN revtype TINYINT COMMENT 'Revision type: 0=ADD, 1=MOD, 2=DEL';
ALTER TABLE process_payments_requests_aud MODIFY COLUMN rev INT COMMENT 'Foreign key to revinfo table for revision tracking';

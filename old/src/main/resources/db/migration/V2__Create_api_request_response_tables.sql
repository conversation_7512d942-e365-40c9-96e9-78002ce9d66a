-- Migration V2: Create API request and response tables for comprehensive API logging
-- Creates separate tables for request and response data with proper foreign key relationship

-- Create api_request table for outgoing API request data
CREATE TABLE api_request (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    request_id VARCHAR(255) NOT NULL UNIQUE,
    url VARCHAR(1000) NOT NULL,
    http_method VARCHAR(10) NOT NULL,
    request_headers TEXT,
    request_body TEXT,
    request_timestamp TIMESTAMP NOT NULL,
    user_agent VARCHAR(500),
    ip_address VARCHAR(45),
    session_id VARCHAR(100),
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM',
    updated_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM'
);

-- Create api_response table for incoming API response data
CREATE TABLE api_response (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    api_request_id BIGINT NULL, -- Nullable for webhook responses without corresponding requests
    response_status_code INT NOT NULL,
    response_headers TEXT,
    response_body TEXT,
    response_timestamp TIMESTAMP NOT NULL,
    response_size BIGINT,
    processing_time_ms BIGINT,
    error_message VARCHAR(1000),
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM',
    updated_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM'
);

-- Add foreign key constraint for proper relational structure
ALTER TABLE api_response ADD CONSTRAINT fk_api_response_request
    FOREIGN KEY (api_request_id) REFERENCES api_request(id) ON DELETE SET NULL;

-- Create indexes for optimal query performance
CREATE UNIQUE INDEX idx_api_request_request_id ON api_request(request_id);
CREATE INDEX idx_api_request_timestamp ON api_request(request_timestamp);
CREATE INDEX idx_api_request_url ON api_request(url(255));

CREATE INDEX idx_api_response_request_id ON api_response(api_request_id);
CREATE INDEX idx_api_response_timestamp ON api_response(response_timestamp);
CREATE INDEX idx_api_response_status_code ON api_response(response_status_code);

-- Composite indexes for common query patterns
CREATE INDEX idx_api_request_monitoring ON api_request(request_timestamp, url(255));
CREATE INDEX idx_api_response_monitoring ON api_response(response_status_code, response_timestamp);
CREATE INDEX idx_api_response_performance ON api_response(api_request_id, response_timestamp);

-- Add comments for documentation
ALTER TABLE api_request COMMENT = 'Stores outgoing API request data with comprehensive audit trail';
ALTER TABLE api_response COMMENT = 'Stores incoming API response data linked to requests via foreign key';

-- Column comments for api_request table
ALTER TABLE api_request MODIFY COLUMN request_id VARCHAR(255) COMMENT 'Unique identifier for tracking request-response pairs';
ALTER TABLE api_request MODIFY COLUMN url VARCHAR(1000) COMMENT 'Full request URL including query parameters';
ALTER TABLE api_request MODIFY COLUMN http_method VARCHAR(10) COMMENT 'HTTP method (GET, POST, PUT, DELETE, etc.)';
ALTER TABLE api_request MODIFY COLUMN request_headers TEXT COMMENT 'HTTP request headers as JSON or text';
ALTER TABLE api_request MODIFY COLUMN request_body TEXT COMMENT 'Complete serialized request object as JSON';
ALTER TABLE api_request MODIFY COLUMN request_timestamp TIMESTAMP COMMENT 'Exact timestamp when request was sent';
ALTER TABLE api_request MODIFY COLUMN user_agent VARCHAR(500) COMMENT 'User agent string from the request';
ALTER TABLE api_request MODIFY COLUMN ip_address VARCHAR(45) COMMENT 'IP address of the client (supports IPv6)';
ALTER TABLE api_request MODIFY COLUMN session_id VARCHAR(100) COMMENT 'Session identifier for tracking user sessions';

-- Column comments for api_response table
ALTER TABLE api_response MODIFY COLUMN api_request_id BIGINT COMMENT 'Foreign key to api_request table, NULL for webhook responses';
ALTER TABLE api_response MODIFY COLUMN response_status_code INT COMMENT 'HTTP status code (200, 404, 500, etc.)';
ALTER TABLE api_response MODIFY COLUMN response_headers TEXT COMMENT 'HTTP response headers as JSON or text';
ALTER TABLE api_response MODIFY COLUMN response_body TEXT COMMENT 'Complete serialized response object as JSON';
ALTER TABLE api_response MODIFY COLUMN response_timestamp TIMESTAMP COMMENT 'Exact timestamp when response was received';
ALTER TABLE api_response MODIFY COLUMN response_size BIGINT COMMENT 'Size of the response body in bytes';
ALTER TABLE api_response MODIFY COLUMN processing_time_ms BIGINT COMMENT 'Time taken to process the request in milliseconds';
ALTER TABLE api_response MODIFY COLUMN error_message VARCHAR(1000) COMMENT 'Error message if the response indicates an error';

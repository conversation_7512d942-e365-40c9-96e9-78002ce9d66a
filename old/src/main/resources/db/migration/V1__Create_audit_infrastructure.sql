-- Migration V1: Create audit infrastructure for Hibernate Envers
-- This migration creates the base audit infrastructure required for entity change tracking

-- Revision information table for audit tracking
-- Note: rev field must be AUTO_INCREMENT for Hibernate Envers DefaultRevisionEntity
CREATE TABLE revinfo (
    rev INT AUTO_INCREMENT PRIMARY KEY,
    revtstmp BIGINT NOT NULL,
    username VARCHAR(100),
    ip_address VARCHAR(45),
    user_agent TEXT
);

-- Add comments for audit infrastructure
ALTER TABLE revinfo COMMENT = 'Revision information for audit tracking with Hibernate Envers';

-- Column comments for revinfo table
ALTER TABLE revinfo MODIFY COLUMN rev INT AUTO_INCREMENT COMMENT 'Unique revision number for each transaction (AUTO_INCREMENT)';
ALTER TABLE revinfo MODIFY COLUMN revtstmp BIGINT COMMENT 'Timestamp of the revision in milliseconds';
ALTER TABLE revinfo MODIFY COLUMN username VARCHAR(100) COMMENT 'Use<PERSON><PERSON> who made the change';
ALTER TABLE revinfo MODIFY COLUMN ip_address VARCHAR(45) COMMENT 'IP address of the user who made the change';
ALTER TABLE revinfo MODIFY COLUMN user_agent TEXT COMMENT 'User agent string of the client';

-- Create index for performance on timestamp queries
CREATE INDEX idx_revinfo_timestamp ON revinfo(revtstmp);

-- Migration V6: Create ManagePaymentControls audit table for change tracking
-- This table is automatically managed by Hibernate Envers for audit trail

CREATE TABLE manage_payment_controls_requests_aud (
    id BIGINT NOT NULL,
    rev INT NOT NULL,
    revtype TINYINT,
    message_id VARCHAR(100),
    client_id VARCHAR(100),
    account_number VARCHAR(50),
    buyer_id VARCHAR(100),
    payment_control_details_data LONGTEXT,
    status VARCHAR(20),
    retry_count INT,
    failure_reason VARCHAR(1000),
    processed_at TIMESTAMP,
    visa_response_data LONGTEXT,
    visa_response_code VARCHAR(10),
    visa_response_description VARCHAR(500),
    created_by VARCHAR(100),
    created_date TIMESTAMP,
    updated_by VA<PERSON>HA<PERSON>(100),
    updated_date TIMESTAMP,
    PRIMARY KEY (id, rev)
);

-- Create index on revision number for audit queries
CREATE INDEX idx_manage_payment_controls_aud_rev ON manage_payment_controls_requests_aud(rev);

-- <PERSON>reate index on message_id for audit trail queries
CREATE INDEX idx_manage_payment_controls_aud_message_id ON manage_payment_controls_requests_aud(message_id);

-- <PERSON>reate index on revision type for filtering audit operations
CREATE INDEX idx_manage_payment_controls_aud_revtype ON manage_payment_controls_requests_aud(revtype);

-- Add foreign key constraint to revision info table (created by Hibernate Envers)
ALTER TABLE manage_payment_controls_requests_aud 
    ADD CONSTRAINT fk_manage_payment_controls_aud_revinfo 
    FOREIGN KEY (rev) REFERENCES revinfo(rev);

-- Comments for documentation
ALTER TABLE manage_payment_controls_requests_aud COMMENT = 'Audit table for ManagePaymentControls requests managed by Hibernate Envers';
ALTER TABLE manage_payment_controls_requests_aud MODIFY COLUMN rev INT COMMENT 'Revision number from Hibernate Envers';
ALTER TABLE manage_payment_controls_requests_aud MODIFY COLUMN revtype TINYINT COMMENT 'Revision type: 0=ADD, 1=MOD, 2=DEL';
ALTER TABLE manage_payment_controls_requests_aud MODIFY COLUMN message_id VARCHAR(100) COMMENT 'Unique message ID from Visa ManagePaymentControls API';
ALTER TABLE manage_payment_controls_requests_aud MODIFY COLUMN status VARCHAR(20) COMMENT 'Processing status at time of revision';
ALTER TABLE manage_payment_controls_requests_aud MODIFY COLUMN retry_count INT COMMENT 'Number of retry attempts at time of revision';
ALTER TABLE manage_payment_controls_requests_aud MODIFY COLUMN visa_response_code VARCHAR(10) COMMENT 'Visa API response code at time of revision';
ALTER TABLE manage_payment_controls_requests_aud MODIFY COLUMN visa_response_description VARCHAR(500) COMMENT 'Visa API response description at time of revision';

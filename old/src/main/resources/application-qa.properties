# QA Environment Configuration
spring.application.name=visa-qa

# MySQL Database Configuration for QA
spring.datasource.url=***********************************************************************************************
spring.datasource.username=visa_qa_user
spring.datasource.password=visa_qa_pass
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Connection Pool Configuration for QA
spring.datasource.hikari.maximum-pool-size=15
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# JPA/Hibernate Configuration for QA
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect

# Flyway Configuration for QA
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=false
spring.flyway.validate-on-migrate=true
spring.flyway.clean-disabled=true

# Logging Configuration for QA
logging.level.com.papertrl.visa=INFO
logging.level.org.hibernate.SQL=WARN
logging.level.org.flywaydb=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.file.name=/var/log/visa/visa-qa.log
logging.file.max-size=100MB
logging.file.max-history=30

# Visa API Configuration for QA
visa.api.base-url=https://cert.api.visa.com
visa.api.timeout=15000
visa.api.connection-timeout=10000
visa.api.read-timeout=30000
visa.api.write-timeout=30000
visa.api.user-id=
visa.api.password=
visa.api.retry.max-attempts=3
visa.api.retry.initial-delay=1000
visa.api.retry.max-delay=10000
visa.api.retry.multiplier=2.0

# SSL Configuration for QA
visa.ssl.enabled=true
visa.ssl.keystore.path=
visa.ssl.keystore.password=
visa.ssl.keystore.type=PKCS12
visa.ssl.key.alias=
visa.ssl.key.password=
visa.ssl.protocol=TLSv1.2
visa.ssl.enabled-protocols=TLSv1.2

# API Logging Configuration for QA
visa.api.logging.enabled=true
visa.api.logging.log-request-body=true
visa.api.logging.log-response-body=true
visa.api.logging.max-body-size=10240
visa.api.logging.truncate-large-bodies=true

# Visa API Endpoints Configuration for QA
visa.api.endpoints.process-payments.path=/vpa/v1/payment/ProcessPayments
visa.api.endpoints.process-payments.success-status-codes=PP001

visa.api.endpoints.manage-payment-controls.path=/vpa/v1/accountManagement/ManagePaymentControls
visa.api.endpoints.manage-payment-controls.success-status-codes=00

visa.api.endpoints.get-payment-controls.path=/vpa/v2/accountManagement/getPaymentControls
visa.api.endpoints.get-payment-controls.success-status-codes=AMGP000

# Web Server Configuration for QA
server.port=8080

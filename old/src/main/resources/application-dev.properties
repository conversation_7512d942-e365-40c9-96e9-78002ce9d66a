# Development Environment Configuration
spring.application.name=visa-dev

# MySQL Database Configuration for Development
spring.datasource.url=*******************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Connection Pool Configuration for Development
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=1200000

# JPA/Hibernate Configuration for Development - USING FLYWAY FOR SCHEMA MANAGEMENT
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# Hibernate Envers Configuration for Audit Trail (Development)
spring.jpa.properties.org.hibernate.envers.audit_table_suffix=_aud
spring.jpa.properties.org.hibernate.envers.revision_field_name=rev
spring.jpa.properties.org.hibernate.envers.revision_type_field_name=revtype
spring.jpa.properties.org.hibernate.envers.store_data_at_delete=true
spring.jpa.properties.org.hibernate.envers.default_schema=
spring.jpa.properties.org.hibernate.envers.track_entities_changed_in_revision=false

# Flyway Configuration for Development - ENABLED FOR CONSISTENT SCHEMA MANAGEMENT
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.flyway.baseline-version=0
spring.flyway.validate-on-migrate=true
spring.flyway.clean-disabled=false
spring.flyway.out-of-order=false

# Visa API Configuration for Development
visa.api.base-url=https://cert.api.visa.com
visa.api.timeout=10000
visa.api.connection-timeout=10000
visa.api.read-timeout=30000
visa.api.write-timeout=30000
visa.api.user-id=2P9LVK8TRIQ9I5NAOFW721t5lV_t0weOQTR8Tr4eggzxgeiAU
visa.api.password=mv2Euz2ke9ZZ9H302gAml9yD
visa.api.retry.max-attempts=2
visa.api.retry.initial-delay=500
visa.api.retry.max-delay=5000
visa.api.retry.multiplier=2.0

# SSL Configuration for Development
visa.ssl.enabled=true
visa.ssl.keystore.path=classpath:certs/api-visa.jks
visa.ssl.keystore.password=Vt$5tRl%^Nj!
visa.ssl.keystore.type=PKCS12
visa.ssl.key.alias=1
visa.ssl.key.password=Vt$5tRl%^Nj!
visa.ssl.protocol=TLSv1.2
visa.ssl.enabled-protocols=TLSv1.2

# API Logging Configuration for Development
visa.api.logging.enabled=true
visa.api.logging.log-request-body=true
visa.api.logging.log-response-body=true
visa.api.logging.max-body-size=10240
visa.api.logging.truncate-large-bodies=true

# Visa API Endpoints Configuration for Development
visa.api.endpoints.process-payments.path=/vpa/v1/payment/ProcessPayments
visa.api.endpoints.process-payments.success-status-codes=PP001

visa.api.endpoints.manage-payment-controls.path=/vpa/v1/accountManagement/ManagePaymentControls
visa.api.endpoints.manage-payment-controls.success-status-codes=00

visa.api.endpoints.get-payment-controls.path=/vpa/v2/accountManagement/getPaymentControls
visa.api.endpoints.get-payment-controls.success-status-codes=AMGP000

# Web Server Configuration for Development
server.port=8080

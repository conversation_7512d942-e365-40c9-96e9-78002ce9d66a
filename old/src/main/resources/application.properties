spring.application.name=visa

# MySQL Database Configuration
spring.datasource.url=*******************************************************************************************************************************
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:root}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Connection Pool Configuration
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=1200000

# JPA/Hibernate Configuration - USING FLYWAY FOR SCHEMA MANAGEMENT
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

# Hibernate Envers Configuration for Audit Trail
spring.jpa.properties.org.hibernate.envers.audit_table_suffix=_aud
spring.jpa.properties.org.hibernate.envers.revision_field_name=rev
spring.jpa.properties.org.hibernate.envers.revision_type_field_name=revtype
spring.jpa.properties.org.hibernate.envers.store_data_at_delete=true
spring.jpa.properties.org.hibernate.envers.default_schema=
spring.jpa.properties.org.hibernate.envers.track_entities_changed_in_revision=false

# Flyway Configuration - ENABLED FOR CONSISTENT SCHEMA MANAGEMENT
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.flyway.baseline-version=0
spring.flyway.validate-on-migrate=true
spring.flyway.out-of-order=false

# Visa API Configuration
visa.api.base-url=${VISA_API_BASE_URL:https://cert.api.visa.com}
visa.api.timeout=${VISA_API_TIMEOUT:30000}
visa.api.connection-timeout=${VISA_API_CONNECTION_TIMEOUT:10000}
visa.api.read-timeout=${VISA_API_READ_TIMEOUT:30000}
visa.api.write-timeout=${VISA_API_WRITE_TIMEOUT:30000}
visa.api.user-id=${VISA_API_USER_ID:}
visa.api.password=${VISA_API_PASSWORD:}
visa.api.retry.max-attempts=${VISA_API_MAX_RETRIES:3}
visa.api.retry.initial-delay=${VISA_API_INITIAL_DELAY:1000}
visa.api.retry.max-delay=${VISA_API_MAX_DELAY:10000}
visa.api.retry.multiplier=${VISA_API_MULTIPLIER:2.0}

# SSL/TLS Configuration for Visa API
visa.ssl.enabled=${VISA_SSL_ENABLED:false}
visa.ssl.keystore.path=${VISA_KEYSTORE_PATH:}
visa.ssl.keystore.password=${VISA_KEYSTORE_PASSWORD:}
visa.ssl.keystore.type=${VISA_KEYSTORE_TYPE:PKCS12}
visa.ssl.key.alias=${VISA_KEY_ALIAS:}
visa.ssl.key.password=${VISA_KEY_PASSWORD:}
visa.ssl.protocol=${VISA_SSL_PROTOCOL:TLSv1.2}
visa.ssl.enabled-protocols=${VISA_SSL_ENABLED_PROTOCOLS:TLSv1.2}
visa.ssl.use-same-keystore-for-trust=${VISA_SSL_USE_SAME_KEYSTORE:true}
visa.ssl.verify-hostname=${VISA_SSL_VERIFY_HOSTNAME:true}

# API Logging Configuration
visa.api.logging.enabled=${VISA_API_LOGGING_ENABLED:true}
visa.api.logging.log-request-body=${VISA_API_LOG_REQUEST_BODY:true}
visa.api.logging.log-response-body=${VISA_API_LOG_RESPONSE_BODY:true}
visa.api.logging.max-body-size=${VISA_API_MAX_BODY_SIZE:10240}
visa.api.logging.truncate-large-bodies=${VISA_API_TRUNCATE_LARGE_BODIES:true}

# Visa API Endpoints Configuration
visa.api.endpoints.process-payments.path=${VISA_API_PROCESS_PAYMENTS_PATH:/vpa/v1/payment/ProcessPayments}
visa.api.endpoints.process-payments.success-status-codes=${VISA_API_PROCESS_PAYMENTS_SUCCESS_CODES:PP001}

visa.api.endpoints.manage-payment-controls.path=${VISA_API_MANAGE_PAYMENT_CONTROLS_PATH:/vpa/v1/accountManagement/ManagePaymentControls}
visa.api.endpoints.manage-payment-controls.success-status-codes=${VISA_API_MANAGE_PAYMENT_CONTROLS_SUCCESS_CODES:00}

visa.api.endpoints.get-payment-controls.path=${VISA_API_GET_PAYMENT_CONTROLS_PATH:/vpa/v2/accountManagement/getPaymentControls}
visa.api.endpoints.get-payment-controls.success-status-codes=${VISA_API_GET_PAYMENT_CONTROLS_SUCCESS_CODES:AMGP000}

# Web Server Configuration
server.port=${SERVER_PORT:8080}
server.servlet.context-path=${CONTEXT_PATH:}

# Management Endpoints
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=when-authorized

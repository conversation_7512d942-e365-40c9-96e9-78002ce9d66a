# Production Environment Configuration
#
# IMPORTANT: This file uses environment variables for security.
# Required environment variables:
# - DB_PASSWORD: Database password (no default)
# - VISA_API_BASE_URL: Visa API base URL (no default)
# - VISA_KEYSTORE_PATH: Path to SSL keystore (no default)
# - VISA_KEYSTORE_PASSWORD: SSL keystore password (no default)
# - VISA_KEY_ALIAS: SSL key alias (no default)
# - VISA_KEY_PASSWORD: SSL key password (no default)
#
spring.application.name=visa-prod

# MySQL Database Configuration for Production
spring.datasource.url=${DB_URL:*************************************************************************************************}
spring.datasource.username=${DB_USERNAME:visa_prod_user}
spring.datasource.password=${DB_PASSWORD}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Connection Pool Configuration for Production
spring.datasource.hikari.maximum-pool-size=${DB_POOL_MAX_SIZE:20}
spring.datasource.hikari.minimum-idle=${DB_POOL_MIN_IDLE:10}
spring.datasource.hikari.connection-timeout=${DB_CONNECTION_TIMEOUT:30000}
spring.datasource.hikari.idle-timeout=${DB_IDLE_TIMEOUT:600000}
spring.datasource.hikari.max-lifetime=${DB_MAX_LIFETIME:1800000}
spring.datasource.hikari.leak-detection-threshold=${DB_LEAK_DETECTION:60000}

# JPA/Hibernate Configuration for Production
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect

# Flyway Configuration for Production
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=false
spring.flyway.validate-on-migrate=true
spring.flyway.clean-disabled=true
spring.flyway.out-of-order=false

# Logging Configuration for Production
logging.level.com.papertrl.visa=${LOG_LEVEL:INFO}
logging.level.org.hibernate.SQL=WARN
logging.level.org.flywaydb=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.file.name=${LOG_FILE_PATH:/var/log/visa/visa-prod.log}
logging.file.max-size=${LOG_FILE_MAX_SIZE:200MB}
logging.file.max-history=${LOG_FILE_MAX_HISTORY:60}

# Visa API Configuration for Production
visa.api.base-url=${VISA_API_BASE_URL}
visa.api.timeout=${VISA_API_TIMEOUT:30000}
visa.api.connection-timeout=${VISA_API_CONNECTION_TIMEOUT:10000}
visa.api.read-timeout=${VISA_API_READ_TIMEOUT:30000}
visa.api.write-timeout=${VISA_API_WRITE_TIMEOUT:30000}
visa.api.user-id=${VISA_API_USER_ID:}
visa.api.password=${VISA_API_PASSWORD:}
visa.api.retry.max-attempts=${VISA_API_MAX_RETRIES:5}
visa.api.retry.initial-delay=${VISA_API_INITIAL_DELAY:2000}
visa.api.retry.max-delay=${VISA_API_MAX_DELAY:30000}
visa.api.retry.multiplier=${VISA_API_MULTIPLIER:2.0}

# SSL Configuration for Production (required)
visa.ssl.enabled=true
visa.ssl.keystore.path=${VISA_KEYSTORE_PATH}
visa.ssl.keystore.password=${VISA_KEYSTORE_PASSWORD}
visa.ssl.keystore.type=${VISA_KEYSTORE_TYPE:PKCS12}
visa.ssl.key.alias=${VISA_KEY_ALIAS}
visa.ssl.key.password=${VISA_KEY_PASSWORD}
visa.ssl.protocol=TLSv1.2
visa.ssl.enabled-protocols=TLSv1.2
visa.ssl.use-same-keystore-for-trust=true
visa.ssl.verify-hostname=${VISA_SSL_VERIFY_HOSTNAME:true}

# API Logging Configuration for Production
visa.api.logging.enabled=${VISA_API_LOGGING_ENABLED:true}
visa.api.logging.log-request-body=${VISA_API_LOG_REQUEST_BODY:false}
visa.api.logging.log-response-body=${VISA_API_LOG_RESPONSE_BODY:false}
visa.api.logging.max-body-size=${VISA_API_MAX_BODY_SIZE:5120}
visa.api.logging.truncate-large-bodies=${VISA_API_TRUNCATE_LARGE_BODIES:true}

# Visa API Endpoints Configuration for Production
visa.api.endpoints.process-payments.path=${VISA_API_PROCESS_PAYMENTS_PATH:/vpa/v1/payment/ProcessPayments}
visa.api.endpoints.process-payments.success-status-codes=${VISA_API_PROCESS_PAYMENTS_SUCCESS_CODES:PP001}

visa.api.endpoints.manage-payment-controls.path=${VISA_API_MANAGE_PAYMENT_CONTROLS_PATH:/vpa/v1/accountManagement/ManagePaymentControls}
visa.api.endpoints.manage-payment-controls.success-status-codes=${VISA_API_MANAGE_PAYMENT_CONTROLS_SUCCESS_CODES:00}

visa.api.endpoints.get-payment-controls.path=${VISA_API_GET_PAYMENT_CONTROLS_PATH:/vpa/v2/accountManagement/getPaymentControls}
visa.api.endpoints.get-payment-controls.success-status-codes=${VISA_API_GET_PAYMENT_CONTROLS_SUCCESS_CODES:AMGP000}

# Web Server Configuration for Production
server.port=${SERVER_PORT:8080}

# Management Endpoints for Production
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=never

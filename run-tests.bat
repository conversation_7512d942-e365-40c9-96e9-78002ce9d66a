@echo off
echo Running ProcessPayments Tests...

echo.
echo === Compiling project ===
call gradlew.bat compileKotlin compileTestKotlin

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo.
echo === Running JUnit Tests ===
call gradlew.bat test --info

echo.
echo === Test Results ===
if exist build\reports\tests\test\index.html (
    echo Test report available at: build\reports\tests\test\index.html
) else (
    echo No test report generated
)

pause

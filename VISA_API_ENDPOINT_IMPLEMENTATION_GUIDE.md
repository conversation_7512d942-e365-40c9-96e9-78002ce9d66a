# Visa API Endpoint Implementation Guide

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [Development Rules & Standards](#development-rules--standards)
3. [Endpoint Implementation Pattern](#endpoint-implementation-pattern)
4. [Configuration Management](#configuration-management)
5. [Error Handling & Observability](#error-handling--observability)
6. [Testing Strategy](#testing-strategy)
7. [Critical Implementation Notes](#critical-implementation-notes)

---

## 1. Architecture Overview

### Hexagonal Architecture Pattern
This application follows a **12-step hexagonal architecture flow** for Visa API endpoints:

**Complete 12-Step Flow:**
1. **Inbound Port** → Domain validation
2. **Domain validation** → Domain model preparation
3. **Domain** → Visa API DTO mapping
4. **DTO mapping** → Outbound Visa API port
5. **Outbound Visa API port** → External API call
6. **API response** → Domain DTO mapping
7. **Response mapping** → Central domain block
8. **Status evaluation** → Domain status update
9. **Database update** → Persistence layer
10. **Domain processing** → Response preparation
11. **Domain** → Inbound DTO mapping
12. **Inbound port** → Return processed domain model

### Layer Organization
```
src/main/kotlin/com/papertrl/visa/
├── VisaApplication.kt                    # Spring Boot main class
├── domain/                               # Domain Layer (Business Logic)
│   ├── model/
│   │   ├── entity/                       # Domain entities
│   │   ├── enums/                        # Domain enums
│   │   └── dto/                          # Domain DTOs
│   ├── ports/
│   │   ├── incoming/                     # Inbound ports (use cases)
│   │   └── outgoing/                     # Outbound ports (repositories, clients)
│   └── service/                          # Domain services
└── infrastructure/                       # Infrastructure Layer
    ├── incoming/                         # Inbound adapters
    │   ├── adapter/                      # REST controllers
    │   ├── dto/generated/                # OpenAPI generated DTOs
    │   └── mapper/                       # Inbound mappers
    └── outgoing/                         # Outbound adapters
        ├── dbmanager/                    # Database adapters
        ├── visa/                         # Visa API adapters
        │   ├── adapter/                  # API client adapters
        │   ├── client/                   # HTTP clients
        │   ├── config/                   # Configuration
        │   ├── dto/                      # API DTOs
        │   ├── evaluator/                # Status evaluators
        │   ├── mapper/                   # API mappers
        │   └── util/                     # Utilities
        └── ...

src/main/resources/
├── api/
│   └── visa-payments-api.yaml            # OpenAPI specification for all Visa payment endpoints
├── application.yaml                      # Main configuration
└── db/migration/                         # Database migrations
```

---

## 2. Development Rules & Standards

### File Structure Requirements
- **One class per .kt file** - Each class must be defined in its own Kotlin file
- **Proper package organization** - Follow the hexagonal architecture package structure
- **Logical package grouping** - Group related functionality in appropriate packages

### Naming Conventions
- **Domain Models**: Use descriptive names ending with the entity type (e.g., `ProcessPaymentsRequest`)
- **Ports**: Use `UseCase` suffix for inbound ports, descriptive names for outbound ports
- **Services**: Use `DomainService` suffix for domain services
- **Adapters**: Use `Adapter` suffix for infrastructure adapters
- **Mappers**: Use `Mapper` suffix for transformation classes
- **Evaluators**: Use `Evaluator` suffix for status evaluation classes

### MessageId Pattern
- **Auto-generated pattern**: `"PREFIX-${UUID.randomUUID().toString().take(LENGTH)}"`
- **Prefix rules**: Maximum 4 characters including dash
- **Length calculation**: 
  - 3-char prefix (e.g., "PP-"): take(33) for UUID
  - 4-char prefix (e.g., "CP-"): take(32) for UUID
- **Examples**:
  - ProcessPayments: `"PP-${UUID.randomUUID().toString().take(33)}"`
  - CancelPayment: `"CP-${UUID.randomUUID().toString().take(33)}"`
  - ValidateAccount: `"VA-${UUID.randomUUID().toString().take(33)}"`

### Documentation Standards
- **Minimal doc comments** with concise explanations about function purpose
- **No .md files** for implementations
- **Focus on business logic** rather than technical implementation details

### Build Tool Usage
- **Use `gradle/wrapper/gradle-wrapper.jar`** for all Gradle operations
- **Never assume anything** - ask for clarification if unclear
- **Get approval** before adding logic or altering existing code

---

## 3. Endpoint Implementation Pattern

### Required 9 Components for New Visa API Endpoints

#### 1. Endpoint Configuration (application.yaml)
```yaml
visa:
  api:
    endpoint-configs:
      - name: "new-endpoint-name"
        path: /vpa/v1/payment/NewEndpoint
        success-status-code: NE001
        method: POST
        description: Description of the new endpoint
```

#### 2. Domain Model with MessageId Pattern
```kotlin
data class NewEndpointRequest(
    val id: Long? = null,
    val messageId: String = "NE-${UUID.randomUUID().toString().take(33)}",
    // Business fields...
    val status: NewEndpointStatus = NewEndpointStatus.PENDING,
    val failureReason: String? = null,
    val processedAt: LocalDateTime? = null,
    // Audit fields
    val createdBy: String = "SYSTEM",
    val createdDate: LocalDateTime = LocalDateTime.now(),
    val updatedBy: String = "SYSTEM",
    val updatedDate: LocalDateTime = LocalDateTime.now()
) {
    fun withStatus(
        newStatus: NewEndpointStatus,
        failureReason: String? = null,
        processedAt: LocalDateTime? = null
    ): NewEndpointRequest = copy(
        status = newStatus,
        failureReason = failureReason,
        processedAt = processedAt,
        updatedDate = LocalDateTime.now()
    )
}
```

#### 3. Inbound Port Interface
```kotlin
interface NewEndpointUseCase {
    suspend fun processNewEndpoint(request: NewEndpointRequest): NewEndpointRequest
}
```

#### 4. Outbound Port Method
Add method to `VisaApiClient` interface:
```kotlin
suspend fun sendNewEndpointRequest(request: NewEndpointRequest): NewEndpointRequest
```

#### 5. Request/Response DTOs
Create DTOs in `infrastructure/outgoing/visa/dto/` package

#### 6. Mapper for Transformations
Create mapper in `infrastructure/outgoing/visa/mapper/` package

#### 7. Domain Service with 12-Step Flow
```kotlin
@Service
class NewEndpointDomainService(
    private val visaApiClient: VisaApiClient,
    private val newEndpointRepository: NewEndpointRepository,
    private val visaStatusCodeEvaluator: VisaStatusCodeEvaluator
) : NewEndpointUseCase {
    
    override suspend fun processNewEndpoint(request: NewEndpointRequest): NewEndpointRequest {
        // Implement 12-step flow...
    }
}
```

#### 8. Adapter Implementation
Implement the outbound port method in `VisaApiClientAdapter`

#### 9. Status Evaluator Methods
Add methods to `VisaStatusCodeEvaluator`:
```kotlin
fun isNewEndpointSuccess(statusCode: String): Boolean
fun isNewEndpointRetryable(statusCode: String): Boolean  
fun getNewEndpointErrorMessage(statusCode: String): String
```

---

## 4. Configuration Management

### List-Based Endpoint Configuration
Use the **List-Based Approach** for scalable endpoint management:

```yaml
visa:
  api:
    # Global configuration
    base-url: ${VISA_API_BASE_URL:https://cert.api.visa.com}
    client-id: ${VISA_API_CLIENT_ID:B2BWS_4_9_4477}
    buyer-id: ${VISA_API_BUYER_ID:041124}
    
    # Endpoint-specific configurations
    endpoint-configs:
      - name: "process-payments"
        path: /vpa/v1/payment/ProcessPayments
        success-status-code: PP001
        method: POST
        description: ProcessPayments API endpoint
      - name: "new-endpoint"
        path: /vpa/v1/payment/NewEndpoint
        success-status-code: NE001
        method: POST
        description: New endpoint description
```

### Configuration Retrieval Pattern
```kotlin
val endpointConfig = httpClientProperties.findEndpointByName("endpoint-name")
    ?: throw IllegalStateException("Endpoint not configured")
val successCodes = endpointConfig.successStatusCode.split(",").map { it.trim() }
```

### Environment Variable Externalization
- **Visa API constants** should be externalized to configuration properties
- **Use environment variable placeholders** rather than hardcoded values
- **Inject at runtime** through services and mappers

---

## 5. Error Handling & Observability

### Error Classification System
The `HttpErrorClassifier` categorizes errors as:

**Retryable Errors:**
- Timeout exceptions (SocketTimeoutException, ReadTimeoutException)
- Network connection issues (ConnectException)
- HTTP 5xx server errors
- HTTP 429 (Too Many Requests)
- HTTP 502, 503, 504 (Gateway errors)

**Non-Retryable Errors:**
- HTTP 4xx client errors (except 429)
- SSL/Certificate errors
- Authentication failures
- Unknown error types (conservative approach)

### Exponential Backoff Retry Logic
```kotlin
private fun calculateDelay(attempt: Int, initialDelay: Long, maxDelay: Long, multiplier: Double): Long {
    val exponentialDelay = (initialDelay * multiplier.pow(attempt - 1)).toLong()
    return min(exponentialDelay, maxDelay)
}
```

### Database Audit Trail
**Complete observability** through database logging:
- **api_request table**: Stores all outbound API requests
- **api_response table**: Stores all API responses (including errors)
- **Request status tracking**: PENDING → PROCESSING → SUCCESS/FAILED
- **Error message logging**: Detailed error descriptions
- **Retry count tracking**: Number of retry attempts

### Status Evaluation Pattern
```kotlin
// Evaluate Visa API response status
if (!visaStatusCodeEvaluator.isEndpointSuccess(responseDto.statusCode)) {
    val errorMessage = visaStatusCodeEvaluator.getEndpointErrorMessage(responseDto.statusCode)
    
    if (visaStatusCodeEvaluator.isEndpointRetryable(responseDto.statusCode)) {
        throw RuntimeException("Visa API retryable error: $errorMessage")
    } else {
        throw RuntimeException("Visa API permanent error: $errorMessage")
    }
}
```

---

## 6. Testing Strategy

### PostgreSQL Database Testing
- **Use PostgreSQL** with dev profile credentials for testing
- **Database configuration**:
  ```yaml
  spring:
    datasource:
      url: *****************************************
      username: postgres
      password: root
  ```

### Clean Test State
**Truncate all database tables** before testing:
```kotlin
@BeforeEach
fun setUp() = runBlocking {
    // Truncate in correct order due to foreign key constraints
    apiResponseJpaRepository.deleteAll()
    apiRequestJpaRepository.deleteAll()
    endpointRepository.deleteAll()
}
```

### Realistic Payload Structures
Use **realistic JSON payload structures** with:
- **Nested payment/supplier/invoice data** that gets flattened to domain model fields
- **Auto-generated messageId** with proper prefix
- **Proper JSON serialization** for complex data fields

### Complete Flow Testing
- **Test complete process flow** without mocking implementations
- **Use real implementations** of all dependencies
- **Verify database persistence** of requests and responses
- **Test error scenarios** and retry logic

### Integration Test Pattern
```kotlin
@SpringBootTest
@ActiveProfiles("test")
class NewEndpointEndToEndIntegrationTest {
    
    @Test
    fun `should execute complete endpoint process flow with real implementations`() = runBlocking {
        // Given - Create valid request
        val request = createValidRequest()
        
        // When - Execute complete flow
        val result = newEndpointUseCase.processEndpoint(request)
        
        // Then - Verify results and database persistence
        assertNotNull(result.id)
        assertEquals(request.messageId, result.messageId)
        // Additional assertions...
    }
}
```

---

## 7. Critical Implementation Notes

### Proven Working Patterns
1. **Follow existing ProcessPayments implementation** as the proven pattern
2. **Use completeRequestResponseLoggingFilter pattern** from WebClientConfig for response body capture
3. **Infrastructure entities should extend domain model classes** rather than duplicating structure
4. **HTTP error responses should be saved** to api_response table for complete logging coverage

### Common Pitfalls to Avoid
1. **Don't create new approaches from scratch** - study and adopt proven working patterns
2. **Don't consume response streams** when capturing response bodies
3. **Remove unnecessary headers** (like X-Request-Body-Captured) before sending to external APIs
4. **Don't hardcode Visa API values** - use configuration properties

### Input Validation Strategy
- **Move validation from domain layer to inbound port level** using validation annotations
- **Use @NotNull, @NotBlank, @Valid, etc.** at the adapter level
- **Only pre-validated, clean data** should enter the domain layer
- **Domain logic should focus on business rules** rather than input sanitization

### Package Management
- **Always use package managers** (Gradle) for dependency management
- **Never manually edit** package configuration files
- **Use `./gradlew` commands** for all dependency operations

### Force Termination for Processes
- **Always use force terminate** for bootRun processes without asking for confirmation
- **Stop Spring Boot applications immediately** when requested

### Architecture Compliance
- **Maintain hexagonal architecture boundaries** - infrastructure concerns stay in infrastructure layer
- **Domain models should be framework-agnostic** - no JPA annotations or external dependencies
- **Use dependency injection** for all cross-layer communications
- **Follow the 12-step flow pattern** for all new endpoints

---

## Quick Reference Checklist

When implementing a new Visa API endpoint, ensure you have:

- [ ] Added endpoint configuration to application.yaml endpoint-configs array
- [ ] Created domain model with proper messageId pattern and audit fields
- [ ] Defined inbound port interface (UseCase)
- [ ] Added outbound port method to VisaApiClient
- [ ] Created request/response DTOs
- [ ] Implemented mapper for domain ↔ DTO transformations
- [ ] Created domain service implementing the 12-step flow
- [ ] Implemented adapter method in VisaApiClientAdapter
- [ ] Added status evaluator methods for the new endpoint
- [ ] Created comprehensive integration tests with PostgreSQL
- [ ] Verified error handling and retry logic
- [ ] Tested complete flow without mocking

This guide provides the complete foundation for implementing new Visa API endpoints following the established patterns and architecture of this application.

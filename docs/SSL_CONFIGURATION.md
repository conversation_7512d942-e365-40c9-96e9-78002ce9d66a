# SSL/TLS Security Infrastructure

## Overview

This document describes the SSL/TLS security infrastructure implemented for secure communication with the Visa API. The implementation follows Spring Boot best practices and provides comprehensive certificate management and secure connection configuration.

## Architecture

The SSL configuration follows the hexagonal architecture pattern with the configuration placed in the infrastructure layer:

```
src/main/kotlin/com/papertrl/visa/infrastructure/config/
├── SslConfig.kt                          # Main SSL configuration class
└── properties/
    ├── SslProperties.kt                  # SSL configuration properties
    ├── KeystoreProperties.kt             # Keystore configuration properties
    └── KeyProperties.kt                  # Key configuration properties
```

## Components

### 1. SslConfig

The main configuration class that:
- Creates SSL-enabled WebClient beans for Visa API communication
- Manages SSL context creation from keystore configuration
- Handles certificate loading and validation
- Provides fallback to standard WebClient when <PERSON><PERSON> is disabled

### 2. SslProperties

SSL configuration properties class with `@ConfigurationProperties` binding:
- `enabled`: Boolean flag to enable/disable SSL
- `keystore`: Keystore configuration properties
- `key`: Private key configuration properties
- `protocol`: SSL protocol version (default: TLSv1.2)
- `enabledProtocols`: Comma-separated list of enabled protocols
- `useSameKeystoreForTrust`: Use keystore as truststore
- `verifyHostname`: Enable hostname verification

### 3. KeystoreProperties

Keystore configuration properties data class:
- `path`: Path to keystore file (supports classpath: and file: prefixes)
- `password`: Keystore password
- `type`: Keystore type (PKCS12, JKS, etc.)

### 4. KeyProperties

Key configuration properties data class:
- `alias`: Key alias in keystore
- `password`: Private key password

## Configuration

### Environment-Specific Configuration

#### Development (application-dev.yaml)
```yaml
visa:
  ssl:
    enabled: true
    keystore:
      path: classpath:certs/api-visa.jks
      password: Vt$5tRl%^Nj!
      type: PKCS12
    key:
      alias: "1"
      password: Vt$5tRl%^Nj!
    protocol: TLSv1.2
    enabled-protocols: TLSv1.2
```

#### Production (application-prod.yaml)
```yaml
visa:
  ssl:
    enabled: true
    keystore:
      path: ${VISA_KEYSTORE_PATH}
      password: ${VISA_KEYSTORE_PASSWORD}
      type: ${VISA_KEYSTORE_TYPE:PKCS12}
    key:
      alias: ${VISA_KEY_ALIAS}
      password: ${VISA_KEY_PASSWORD}
    protocol: TLSv1.2
    enabled-protocols: TLSv1.2
    use-same-keystore-for-trust: true
    verify-hostname: ${VISA_SSL_VERIFY_HOSTNAME:true}
```

### Required Environment Variables (Production)

- `VISA_KEYSTORE_PATH`: Path to SSL keystore file
- `VISA_KEYSTORE_PASSWORD`: Keystore password
- `VISA_KEY_ALIAS`: Key alias in keystore
- `VISA_KEY_PASSWORD`: Private key password
- `VISA_SSL_VERIFY_HOSTNAME`: Enable hostname verification (optional, default: true)

## Certificate Management

### Certificate Location
- Development: `src/main/resources/certs/api-visa.jks`
- Production: Specified via `VISA_KEYSTORE_PATH` environment variable

### Certificate Properties
- Type: PKCS12 (recommended) or JKS
- Contains both client certificate and private key
- Used for mutual TLS authentication with Visa API

### Certificate Renewal
1. Obtain new certificate from Visa
2. Update keystore file
3. Update key alias if changed
4. Restart application

## Usage

### WebClient Bean
The SSL configuration automatically creates a `visaApiWebClient` bean that can be injected into services:

```kotlin
@Service
class VisaApiService(
    @Qualifier("visaApiWebClient") private val webClient: WebClient
) {
    // Use webClient for Visa API calls
}
```

### SSL Context Creation
The SSL context is created automatically when SSL is enabled:
1. Keystore is loaded from configured path
2. KeyManagerFactory is initialized with private key
3. TrustManagerFactory is initialized (optionally from same keystore)
4. SSL context is created and configured
5. WebClient is configured with SSL-enabled HttpClient

## Security Considerations

### Protocol Security
- Uses TLS 1.2 by default (configurable)
- Supports multiple protocols via `enabled-protocols` configuration
- Hostname verification enabled by default

### Certificate Security
- Private key passwords are externalized to environment variables
- Keystore passwords are not logged
- Certificate validation is performed during SSL context creation

### Error Handling
- Comprehensive error handling for certificate loading failures
- Detailed logging for troubleshooting (without sensitive information)
- Graceful fallback to non-SSL mode when disabled

## Testing

### Unit Tests
- SSL configuration creation with valid certificates
- Error handling for missing certificates
- Property binding validation
- WebClient creation with and without SSL

### Integration Tests
- End-to-end SSL handshake testing
- Certificate validation testing
- Visa API connectivity testing

## Troubleshooting

### Common Issues

1. **Certificate Not Found**
   - Verify keystore path is correct
   - Check file permissions
   - Ensure certificate file exists

2. **Invalid Certificate Password**
   - Verify keystore password
   - Check private key password
   - Ensure passwords match certificate configuration

3. **SSL Handshake Failures**
   - Verify certificate validity and expiration
   - Check protocol compatibility
   - Validate hostname verification settings

### Logging
Enable debug logging for SSL troubleshooting:
```yaml
logging:
  level:
    com.papertrl.visa.infrastructure.config.SslConfig: DEBUG
    javax.net.ssl: DEBUG
```

## Compliance

This SSL implementation ensures:
- Secure communication with Visa API
- Mutual TLS authentication
- Industry-standard encryption protocols
- Certificate-based authentication
- Compliance with Visa security requirements

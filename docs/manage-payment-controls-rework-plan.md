# Detailed Rework Plan: ManagePaymentControls Implementation

## 1. Over-Engineering Analysis & Simplification Strategy

**Architectural Principle:** Maintain hexagonal architecture boundaries while simplifying implementation complexity.

### **Over-Engineered Components to Simplify (Preserving Hexagonal Architecture):**

#### **A. PaymentControlRuleMerger (140 lines → ~30 lines)**
- **Current:** Complex nested merging with multiple layers of rule processing
- **Expected:** Simple override extraction within domain layer
- **Action:** Replace with `PaymentControlOverrideExtractor` (domain service)
- **Hexagonal Boundary:** Remains in domain layer, no infrastructure dependencies

#### **B. ManagePaymentControlsInboundMapper (93 lines → ~40 lines)**
- **Current:** Two methods with complex JSON parsing from comment field
- **Expected:** Simple field mapping with database lookup through repository abstraction
- **Action:** Simplify to single method, use domain service for account lookup
- **Hexagonal Boundary:** Infrastructure adapter, calls domain services through interfaces

#### **C. ManagePaymentControlsDomainService Verbose Process**
- **Current:** 12-step process with verbose comments and unnecessary orchestration complexity
- **Expected:** Streamlined flow maintaining hexagonal boundaries: Inbound Port → Domain Logic → Outbound Ports
- **Action:** Simplify orchestration while preserving architectural separation
- **Hexagonal Boundary:** Pure domain logic, uses only abstractions (ports)

---

## 2. Priority-Based Task Breakdown

### **PRIORITY 1: Foundation (Database Lookup)**
*Dependency: None | Impact: Critical*

#### **Task 1.1: Create Account Lookup Service (Domain Layer)**
**Files to Create:**
- `src/main/kotlin/com/papertrl/visa/domain/service/AccountLookupService.kt`

**Hexagonal Architecture Position:** Domain Service (uses outbound port abstraction)

**Implementation:**
```kotlin
@Service
class AccountLookupService(
    private val processPaymentsRepository: ProcessPaymentsRepository // Outbound port abstraction
) {
    suspend fun getAccountNumberByTpId(tpId: String): String {
        return processPaymentsRepository.findByMessageId(tpId)?.accountNumber
            ?: throw IllegalArgumentException("No account found for tpId: $tpId")
    }
}
```

**Architectural Notes:**
- Pure domain service with no infrastructure dependencies
- Uses repository abstraction (outbound port) for data access
- Maintains hexagonal boundary separation

**Validation Steps:**
1. Create unit test with mock repository (test domain logic only)
2. Test with valid tpId returns accountNumber
3. Test with invalid tpId throws exception
4. Verify repository abstraction is called correctly (no direct database access)

---

#### **Task 1.2: Update ManagePaymentControlsInboundMapper (Infrastructure Adapter)**
**Files to Modify:**
- `src/main/kotlin/com/papertrl/visa/infrastructure/incoming/mapper/ManagePaymentControlsInboundMapper.kt`

**Hexagonal Architecture Position:** Infrastructure Adapter (inbound, converts external DTOs to domain models)

**Changes:**
1. Remove `toDomainWithDetails` method (over-engineered)
2. Remove `ManagePaymentControlsRequestWithDetails` data class
3. Update `toDomain` method to use domain service for account lookup
4. Inject `AccountLookupService` (domain service)

**Expected Outcome:**
```kotlin
@Component
class ManagePaymentControlsInboundMapper(
    private val accountLookupService: AccountLookupService // Domain service injection
) {
    suspend fun toDomain(dto: PaymentTransactionDto, idempotencyKey: String, tenantId: String): ManagePaymentControlsRequest {
        val accountNumber = accountLookupService.getAccountNumberByTpId(
            dto.tpId ?: throw IllegalArgumentException("tpId is required")
        )
        val expireOn = dto.expireOn?.toLocalDate()
            ?: throw IllegalArgumentException("expireOn is required")

        return ManagePaymentControlsRequest(
            idempotencyKey = idempotencyKey,
            tenantId = tenantId,
            accountNumber = accountNumber,
            expireOn = expireOn
        )
    }
}
```

**Architectural Notes:**
- Infrastructure adapter that converts external DTOs to domain models
- Uses domain service for business logic (account lookup)
- Maintains separation: adapter handles conversion, domain service handles business logic

**Validation Steps:**
1. Test with valid tpId retrieves correct accountNumber through domain service
2. Test with missing tpId throws exception
3. Test with invalid tpId throws exception (from domain service)
4. Verify expireOn conversion works correctly
5. Ensure no direct infrastructure dependencies in domain logic

---

### **PRIORITY 2: Core Logic (Override Extraction & Processing)**
*Dependency: Priority 1 | Impact: High*

#### **Task 2.1: Create Simple Override Extractor (Domain Service)**
**Files to Create:**
- `src/main/kotlin/com/papertrl/visa/domain/service/PaymentControlOverrideExtractor.kt`

**Hexagonal Architecture Position:** Domain Service (pure business logic, no infrastructure dependencies)

**Implementation:**
```kotlin
@Service
class PaymentControlOverrideExtractor {

    private val requiredOverrides = setOf(
        "spendLimitAmount", "maxAuth", "startDate",
        "amountCurrencyCode", "endDate", "rangeType"
    )

    fun extractOverrides(paymentControlDetails: List<PaymentControlDetailDto>?): Map<String, String> {
        if (paymentControlDetails.isNullOrEmpty()) return emptyMap()

        return paymentControlDetails
            .flatMap { it.rulesSet }
            .flatMap { it.rules }
            .flatMap { it.overrides }
            .filter { requiredOverrides.contains(it.overrideCode) }
            .associate { it.overrideCode to it.overrideValue }
    }
}
```

**Architectural Notes:**
- Pure domain service with business logic for override extraction
- No infrastructure dependencies (works with domain DTOs)
- Encapsulates business rules about which overrides are required
- Can be easily unit tested in isolation

**Validation Steps:**
1. Test with null/empty input returns empty map
2. Test with valid input extracts only required overrides
3. Test filters out non-required overrides
4. Verify map structure is correct
5. Ensure no infrastructure dependencies

---

#### **Task 2.2: Create ExpireOn Processor (Domain Service)**
**Files to Create:**
- `src/main/kotlin/com/papertrl/visa/domain/service/ExpireOnProcessor.kt`

**Hexagonal Architecture Position:** Domain Service (pure business logic for date processing)

**Implementation:**
```kotlin
@Service
class ExpireOnProcessor {

    fun processExpireOn(expireOn: LocalDate, overrides: Map<String, String>): String {
        return if (overrides.containsKey("endDate")) {
            // If endDate override exists, format as MM/DD/YYYY
            expireOn.format(DateTimeFormatter.ofPattern("MM/dd/yyyy"))
        } else {
            // Otherwise, format as YYYY-MM-DD
            expireOn.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
        }
    }

    fun getEndDateForPaymentControlDetails(expireOn: LocalDate): String {
        // Always format as YYYY-MM-DD for paymentControlDetails.endDate
        return expireOn.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
    }
}
```

**Architectural Notes:**
- Pure domain service encapsulating date formatting business rules
- No infrastructure dependencies (uses standard Java time APIs)
- Encapsulates complex business logic about date format requirements
- Easily testable and maintainable

**Validation Steps:**
1. Test with endDate override returns MM/DD/YYYY format
2. Test without endDate override returns YYYY-MM-DD format
3. Test date formatting is correct for various dates
4. Test getEndDateForPaymentControlDetails always returns YYYY-MM-DD
5. Verify no infrastructure dependencies

---

### **PRIORITY 3: Integration (Update Domain Service)**
*Dependency: Priority 1 & 2 | Impact: High*

#### **Task 3.1: Streamline ManagePaymentControlsDomainService (Domain Layer)**
**Files to Modify:**
- `src/main/kotlin/com/papertrl/visa/domain/service/ManagePaymentControlsDomainService.kt`

**Hexagonal Architecture Position:** Domain Service (implements inbound port, orchestrates domain logic)

**Changes:**
1. Remove dependency on `PaymentControlRuleMerger`
2. Add dependencies on domain services: `PaymentControlOverrideExtractor`, `ExpireOnProcessor`, `PaymentControlRequestBuilder`
3. Streamline orchestration while maintaining hexagonal boundaries
4. Remove verbose 12-step comments, keep essential flow documentation

**Expected Streamlined Flow (Maintaining Hexagonal Architecture):**
```kotlin
// Domain Service implementing inbound port
@Service
class ManagePaymentControlsDomainService(
    // Outbound ports (abstractions)
    private val visaApiClient: VisaApiClient,
    private val managePaymentControlsRepository: ManagePaymentControlsRepository,
    private val getPaymentControlsUseCase: GetPaymentControlsUseCase,
    // Domain services (pure business logic)
    private val paymentControlOverrideExtractor: PaymentControlOverrideExtractor,
    private val expireOnProcessor: ExpireOnProcessor,
    private val paymentControlRequestBuilder: PaymentControlRequestBuilder,
    private val objectMapper: ObjectMapper
) : ManagePaymentControlsUseCase {

    override suspend fun managePaymentControls(request: ManagePaymentControlsRequest): ManagePaymentControlsRequest {
        // Idempotency check through repository abstraction
        val existingRequest = managePaymentControlsRepository.findByIdempotencyKeyAndStatus(
            request.idempotencyKey, VisaRequestStatus.SUCCESS
        )
        if (existingRequest != null) return existingRequest

        // Persist request through repository abstraction
        val savedRequest = managePaymentControlsRepository.save(request)
        val processingRequest = updateRequestStatus(savedRequest, VisaRequestStatus.PROCESSING)

        try {
            // Retrieve existing controls through use case abstraction
            val retrievedControls = getPaymentControlsUseCase.getPaymentControls(
                GetPaymentControlsRequest(processingRequest.tenantId, processingRequest.accountNumber)
            )
            val retrievedDetails = extractPaymentControlDetailsFromResponse(retrievedControls)

            // Domain logic: extract overrides and process dates
            val overrides = paymentControlOverrideExtractor.extractOverrides(retrievedDetails)
            val endDate = expireOnProcessor.getEndDateForPaymentControlDetails(request.expireOn)

            // Domain logic: build final request structure
            val finalPaymentControlDetails = paymentControlRequestBuilder.buildPaymentControlDetails(
                overrides, endDate, request
            )

            // Send through API client abstraction
            val responseRequest = visaApiClient.sendManagePaymentControlsRequest(
                processingRequest, finalPaymentControlDetails
            )

            return evaluateAndUpdateRequestStatus(responseRequest)

        } catch (e: Exception) {
            return handleManagePaymentControlsError(processingRequest, e)
        }
    }
}
```

**Architectural Notes:**
- Implements inbound port (ManagePaymentControlsUseCase)
- Uses only abstractions for outbound dependencies (repository, API client, other use cases)
- Orchestrates domain services for business logic
- Maintains clean separation between domain logic and infrastructure concerns

**Validation Steps:**
1. Test idempotency check works through repository abstraction
2. Test GetPaymentControls integration through use case abstraction
3. Test domain services integration (override extraction, date processing)
4. Test API client integration through abstraction
5. Verify no direct infrastructure dependencies in domain logic
6. Test error handling maintains architectural boundaries

---

#### **Task 3.2: Create Request Builder (Domain Service)**
**Files to Create:**
- `src/main/kotlin/com/papertrl/visa/domain/service/PaymentControlRequestBuilder.kt`

**Hexagonal Architecture Position:** Domain Service (pure business logic for request construction)

**Implementation:**
```kotlin
@Service
class PaymentControlRequestBuilder {

    fun buildPaymentControlDetails(
        overrides: Map<String, String>,
        endDate: String,
        request: ManagePaymentControlsRequest,
        startDate: String = "06/12/2025", // Default or from overrides
        timeZone: String = "UTC-0",
        mcgRuleAction: String = "Block"
    ): List<PaymentControlDetailDto> {

        val overridesList = overrides.map { (code, value) ->
            OverrideDto(
                sequence = "0",
                overrideCode = code,
                overrideValue = value
            )
        }

        val rule = RuleDto(
            ruleCode = "SPV",
            overrides = overridesList
        )

        val rulesSet = RulesSetDto(
            action = "R",
            rules = listOf(rule)
        )

        return listOf(
            PaymentControlDetailDto(
                rulesSet = listOf(rulesSet),
                endDate = endDate,
                mcgRuleAction = mcgRuleAction,
                timeZone = timeZone,
                startDate = startDate
            )
        )
    }
}
```

**Architectural Notes:**
- Pure domain service encapsulating request building business logic
- No infrastructure dependencies
- Encapsulates knowledge of VISA API structure requirements
- Easily testable and maintainable

**Validation Steps:**
1. Test builds correct structure with overrides
2. Test default values are applied correctly
3. Test output matches expected JSON structure
4. Test with empty overrides
5. Verify no infrastructure dependencies

---

### **PRIORITY 4: Cleanup & Validation**
*Dependency: Priority 1, 2, 3 | Impact: Medium*

#### **Task 4.1: Remove PaymentControlRuleMerger**
**Files to Delete:**
- `src/main/kotlin/com/papertrl/visa/domain/service/PaymentControlRuleMerger.kt`

**Files to Update:**
- Remove all references to `PaymentControlRuleMerger` from other classes
- Update dependency injection configurations

**Validation Steps:**
1. Ensure no compilation errors after removal
2. Verify all tests still pass
3. Check no unused imports remain

---

#### **Task 4.2: Update Inbound Adapter (Infrastructure Layer)**
**Files to Modify:**
- `src/main/kotlin/com/papertrl/visa/infrastructure/incoming/adapter/ProcessPaymentsInboundAdapter.kt`

**Hexagonal Architecture Position:** Infrastructure Adapter (inbound, handles HTTP requests)

**Changes:**
1. Update `managePaymentControls` method to use simplified mapper
2. Maintain proper hexagonal boundaries: Adapter → Inbound Port → Domain
3. Remove any references to complex mapping logic

**Expected Change (Maintaining Hexagonal Architecture):**
```kotlin
override fun managePaymentControls(paymentTransactionDto: PaymentTransactionDto): ResponseEntity<ManagePaymentControlsResponseDto> {
    return try {
        // Infrastructure Adapter → Domain Model conversion
        val domainRequest = runBlocking {
            managePaymentControlsInboundMapper.toDomain(
                paymentTransactionDto,
                paymentTransactionDto.idempotencyKey,
                paymentTransactionDto.tenantId
            )
        }

        // Call through Inbound Port (use case abstraction)
        val processedRequest = runBlocking {
            managePaymentControlsUseCase.managePaymentControls(domainRequest)
        }

        // Domain Model → Infrastructure Response conversion
        ResponseEntity.ok(managePaymentControlsOutboundMapper.toResponseDto(processedRequest))

    } catch (e: Exception) {
        // Error handling at infrastructure boundary
        handleManagePaymentControlsError(e, paymentTransactionDto.idempotencyKey)
    }
}
```

**Architectural Notes:**
- Infrastructure adapter maintains hexagonal boundaries
- Converts external DTOs to domain models and vice versa
- Calls domain logic through inbound port abstraction
- Handles infrastructure concerns (HTTP, error responses)

**Validation Steps:**
1. Test with valid PaymentTransactionDto flows through hexagonal boundaries
2. Test error handling for missing tpId (domain validation)
3. Test error handling for invalid tpId (domain service error)
4. Verify response structure matches expected format
5. Ensure adapter doesn't contain business logic

---

## 3. Implementation Order & Dependencies (Hexagonal Architecture Flow)

```
DOMAIN LAYER (Core Business Logic):
Task 1.1 (Account Lookup Service) → Task 2.1 (Override Extractor) → Task 2.2 (ExpireOn Processor)
    ↓                                   ↓                              ↓
Task 3.2 (Request Builder) ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ↓
    ↓
Task 3.1 (Streamline Domain Service - orchestrates all domain services)
    ↓
INFRASTRUCTURE LAYER (Adapters):
Task 1.2 (Update Inbound Mapper) → Task 4.2 (Update Adapter)
    ↓
CLEANUP:
Task 4.1 (Remove Over-engineered Components)
```

**Hexagonal Architecture Flow:**
1. **Domain Services First** (1.1, 2.1, 2.2, 3.2) - Pure business logic, no infrastructure dependencies
2. **Domain Orchestration** (3.1) - Coordinates domain services through abstractions
3. **Infrastructure Adapters** (1.2, 4.2) - Convert between external formats and domain models
4. **Cleanup** (4.1) - Remove over-engineered components

## 4. Final Validation Checklist

### **Integration Tests:**
1. **End-to-End Flow Test:**
   - Send PaymentTransactionDto with valid tpId
   - Verify accountNumber lookup from database
   - Verify GetPaymentControls API call
   - Verify override extraction
   - Verify final JSON structure matches expected format

2. **Error Handling Tests:**
   - Invalid tpId → proper error response
   - Missing tpId → proper error response
   - Database lookup failure → proper error handling
   - GetPaymentControls API failure → proper error handling

3. **JSON Structure Validation:**
   - Compare output with expected VISA API format
   - Verify all required fields are present
   - Verify date formats are correct (MM/DD/YYYY vs YYYY-MM-DD)
   - Verify override values are correctly mapped

### **Performance Validation:**
1. Verify database lookup performance
2. Ensure no unnecessary API calls
3. Confirm simplified logic improves processing time

### **Code Quality Validation:**
1. Reduced line count in key components
2. Eliminated unnecessary abstractions
3. Clear, readable code flow
4. Proper error handling and logging

---

## 5. Expected Outcomes

### **Before (Current State):**
- PaymentControlRuleMerger: 140 lines of complex logic
- ManagePaymentControlsInboundMapper: 93 lines with dual methods
- Complex 12-step domain service flow
- No database lookup for accountNumber
- Generic rule merging instead of specific override extraction

### **After (Target State - Hexagonal Architecture Maintained):**
- **Domain Layer:**
  - PaymentControlOverrideExtractor: ~30 lines of pure business logic
  - ExpireOnProcessor: ~20 lines of date formatting logic
  - AccountLookupService: ~15 lines using repository abstraction
  - PaymentControlRequestBuilder: ~40 lines of request construction logic
  - ManagePaymentControlsDomainService: Streamlined orchestration using only abstractions
- **Infrastructure Layer:**
  - ManagePaymentControlsInboundMapper: ~40 lines with single method
  - ProcessPaymentsInboundAdapter: Clean hexagonal boundary handling
- **Architecture Benefits:**
  - Clear separation of concerns maintained
  - Domain logic easily testable in isolation
  - Infrastructure concerns properly abstracted
  - Exact JSON structure matching VISA API specification

**Total Estimated Effort:** 2-3 days for implementation + 1 day for testing and validation

---

## 8. Hexagonal Architecture Compliance Checklist

### **Domain Layer Compliance:**
- ✅ All domain services have no infrastructure dependencies
- ✅ Domain services use only abstractions (ports) for external communication
- ✅ Business logic is encapsulated in domain services
- ✅ Domain models remain pure (no framework annotations)

### **Port Compliance:**
- ✅ Inbound ports define use case contracts (ManagePaymentControlsUseCase)
- ✅ Outbound ports define infrastructure abstractions (repositories, API clients)
- ✅ Ports contain only interfaces, no implementation details

### **Adapter Compliance:**
- ✅ Inbound adapters convert external requests to domain models
- ✅ Outbound adapters implement port interfaces with infrastructure details
- ✅ Adapters contain no business logic
- ✅ Adapters handle infrastructure concerns (HTTP, database, external APIs)

### **Dependency Direction:**
- ✅ Dependencies point inward toward domain layer
- ✅ Domain layer depends only on abstractions
- ✅ Infrastructure layer depends on domain layer
- ✅ No circular dependencies between layers

---

## 6. Risk Mitigation

### **Technical Risks:**
1. **Database Performance:** Account lookup might be slow
   - **Mitigation:** Add database indexing on messageId field
   - **Fallback:** Implement caching layer if needed

2. **API Integration:** GetPaymentControls API changes
   - **Mitigation:** Maintain backward compatibility in override extraction
   - **Fallback:** Graceful degradation with empty overrides

3. **Date Format Issues:** Incorrect date formatting
   - **Mitigation:** Comprehensive unit tests for all date scenarios
   - **Fallback:** Default to YYYY-MM-DD format if parsing fails

### **Business Risks:**
1. **Data Inconsistency:** tpId not found in database
   - **Mitigation:** Clear error messages and proper exception handling
   - **Fallback:** Option to use fallback account lookup mechanism

2. **Regression Issues:** Breaking existing functionality
   - **Mitigation:** Comprehensive integration tests before deployment
   - **Fallback:** Feature flag to switch between old and new implementation

---

## 7. Success Criteria

### **Functional Success:**
- ✅ AccountNumber retrieved from database using tpId
- ✅ Only required overrides extracted from GetPaymentControls response
- ✅ ExpireOn processed correctly based on override logic
- ✅ Final JSON structure matches VISA API specification exactly
- ✅ All error scenarios handled gracefully

### **Non-Functional Success:**
- ✅ Code complexity reduced by 60%+ (measured by cyclomatic complexity)
- ✅ Processing time improved by 20%+ (fewer unnecessary operations)
- ✅ Memory usage optimized (no complex object graphs)
- ✅ Maintainability improved (clear, linear flow)

### **Quality Success:**
- ✅ 100% unit test coverage for new components
- ✅ Integration tests covering all happy path and error scenarios
- ✅ Code review approval from senior developers
- ✅ Performance benchmarks meet or exceed current implementation

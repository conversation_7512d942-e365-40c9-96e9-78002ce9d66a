spring:
  application:
    name: visa-test

  # PostgreSQL Database Configuration for Testing (using dev profile credentials)
  datasource:
    url: *****************************************
    username: postgres
    password: root
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 5
      minimum-idle: 1
      connection-timeout: 10000
      idle-timeout: 300000
      max-lifetime: 600000

  # JPA/Hibernate Configuration for Testing
  jpa:
    hibernate:
      ddl-auto: none  # Use Flyway for schema management
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        # PostgreSQL specific configuration for JSON handling
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        # Hibernate Envers Configuration for Audit Trail (Testing)
        envers:
          audit_table_suffix: _aud
          revision_field_name: rev
          revision_type_field_name: revtype
          store_data_at_delete: true
          default_schema: ""
          track_entities_changed_in_revision: false

  # Flyway Configuration for Testing - Enabled for PostgreSQL
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    baseline-version: 0
    validate-on-migrate: true
    clean-disabled: false
    out-of-order: false

# Logging Configuration for Testing
logging:
  level:
    com.papertrl.visa: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Visa API Configuration for Testing
visa:
  api:
    base-url: https://cert.api.visa.com
    timeout: 5000
    connection-timeout: 3000
    read-timeout: 10000
    write-timeout: 10000
    user-id: 2P9LVK8TRIQ9I5NAOFW721t5lV_t0weOQTR8Tr4eggzxgeiAU
    password: mv2Euz2ke9ZZ9H302gAml9yD
    client-id: B2BWS_4_9_4477
    buyer-id: 041124
    retry:
      max-attempts: 1
      initial-delay: 100
      max-delay: 1000
      multiplier: 1.0
    endpoint-configs:
      - name: "process-payments"
        path: /vpa/v1/payment/ProcessPayments
        success-status-code: PP001
        method: POST
        description: ProcessPayments API endpoint for testing

  # SSL Configuration for Testing - Disabled for easier testing
  ssl:
    enabled: true
    keystore:
      path: classpath:certs/api-visa.jks
      password: Vt$5tRl%^Nj!
      type: PKCS12
    key:
      alias: "1"
      password: Vt$5tRl%^Nj!
    protocol: TLSv1.2
    enabled-protocols: TLSv1.2
    use-same-keystore-for-trust: true
    verify-hostname: false

# Web Server Configuration for Testing
server:
  port: 0  # Random port for testing
  servlet:
    context-path:

# Management Endpoints for Testing
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

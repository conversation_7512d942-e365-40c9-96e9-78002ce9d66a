package com.papertrl.visa.integration

import com.fasterxml.jackson.databind.ObjectMapper
import com.papertrl.visa.domain.model.entity.ProcessPaymentsRequest
import com.papertrl.visa.domain.model.entity.TenantBuyerConfig
import com.papertrl.visa.domain.model.enums.VisaRequestStatus
import com.papertrl.visa.domain.ports.outgoing.dbmanager.TenantBuyerConfigRepository
import com.papertrl.visa.domain.service.TenantBuyerLookupService
import com.papertrl.visa.infrastructure.outgoing.dbmanager.adapter.ProcessPaymentsRepositoryAdapter
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

/**
 * Integration test for tenant-specific buyer ID functionality.
 * Tests the complete flow from tenant configuration to buyer ID lookup.
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class TenantBuyerIntegrationTest {

    @Autowired
    private lateinit var tenantBuyerConfigRepository: TenantBuyerConfigRepository

    @Autowired
    private lateinit var tenantBuyerLookupService: TenantBuyerLookupService

    @Autowired
    private lateinit var processPaymentsRepositoryAdapter: ProcessPaymentsRepositoryAdapter

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Test
    fun `should retrieve tenant buyer configuration when exists in database`() = runBlocking {
        // Note: This test assumes tenant configuration exists in database
        // In real scenarios, administrators would insert data directly via SQL

        // When - Try to lookup a tenant that might exist
        val config = tenantBuyerConfigRepository.findActiveByTenantId("DEFAULT_TENANT")

        // Then - Should find the default tenant configuration
        if (config != null) {
            assertEquals("DEFAULT_TENANT", config.tenantId)
            assertEquals("041124", config.buyerId)
            assertTrue(config.isActive)
        }
    }

    @Test
    fun `should fallback to default buyer ID when tenant not found`() = runBlocking {
        // Given - A tenant that doesn't exist in database
        val nonExistentTenant = "NON_EXISTENT_TENANT_${System.currentTimeMillis()}"

        // When
        val buyerId = tenantBuyerLookupService.getBuyerIdForTenant(nonExistentTenant)

        // Then - Should fallback to default buyer ID from configuration
        assertEquals("041124", buyerId)
    }

    @Test
    fun `should save ProcessPaymentsRequest with tenant ID`() = runBlocking {
        // Given - Create ProcessPaymentsRequest with tenant ID
        val invoicesJson = """[{"invoiceNumber": "INV001", "invoiceAmount": 100, "invoiceDate": "2024-01-01"}]"""
        val request = ProcessPaymentsRequest(
            messageId = "TEST-TENANT-${System.currentTimeMillis()}",
            idempotencyKey = "test-tenant-idempotency-${System.currentTimeMillis()}",
            tenantId = "TEST_TENANT_INTEGRATION",
            actionType = 1,
            paymentExpiryDate = "2024-12-31",
            accountType = 2,
            currencyCode = "USD",
            paymentGrossAmount = BigDecimal("100.00"),
            paymentType = "STP",
            supplierName = "Test Tenant Supplier",
            supplierID = 12345L,
            supplierAddressLine1 = "123 Tenant Street",
            supplierCity = "Tenant City",
            supplierState = "TX",
            supplierCountryCode = "USA",
            supplierPostalCode = "12345",
            primaryEmailAddress = "<EMAIL>",
            invoicesData = objectMapper.readTree(invoicesJson),
            status = ProcessPaymentsStatus.PENDING
        )

        // When
        val savedRequest = processPaymentsRepositoryAdapter.save(request)

        // Then
        assertNotNull(savedRequest.id)
        assertEquals("TEST_TENANT_INTEGRATION", savedRequest.tenantId)

        // Verify the request was saved and can be retrieved
        val retrievedRequest = processPaymentsRepositoryAdapter.findByMessageId(savedRequest.messageId)
        assertNotNull(retrievedRequest)
        assertEquals("TEST_TENANT_INTEGRATION", retrievedRequest!!.tenantId)
    }
}

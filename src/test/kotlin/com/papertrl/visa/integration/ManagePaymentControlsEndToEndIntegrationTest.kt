package com.papertrl.visa.integration

import com.papertrl.visa.domain.ports.incoming.visa.ManagePaymentControlsUseCase
import com.papertrl.visa.infrastructure.incoming.dto.generated.ManagePaymentControlsTransactionDto
import com.papertrl.visa.infrastructure.incoming.dto.generated.PaymentControlDetailDto
import com.papertrl.visa.infrastructure.incoming.dto.generated.RulesSetDto
import com.papertrl.visa.infrastructure.incoming.dto.generated.RuleDto
import com.papertrl.visa.infrastructure.incoming.dto.generated.OverrideDto
import com.papertrl.visa.infrastructure.incoming.mapper.ManagePaymentControlsInboundMapper
import com.papertrl.visa.infrastructure.outgoing.dbmanager.repository.ManagePaymentControlsRequestJpaRepository
import com.papertrl.visa.infrastructure.outgoing.dbmanager.repository.ApiRequestJpaRepository
import com.papertrl.visa.infrastructure.outgoing.dbmanager.repository.ApiResponseJpaRepository
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import kotlin.test.*

/**
 * End-to-end integration test for ManagePaymentControls endpoint.
 * Tests the complete flow from inbound adapter through domain service to database persistence.
 * Uses PostgreSQL database with real implementations - no mocking.
 */
@SpringBootTest
@ActiveProfiles("test")
class ManagePaymentControlsEndToEndIntegrationTest {

    @Autowired
    private lateinit var managePaymentControlsUseCase: ManagePaymentControlsUseCase

    @Autowired
    private lateinit var inboundMapper: ManagePaymentControlsInboundMapper

    @Autowired
    private lateinit var managePaymentControlsRepository: ManagePaymentControlsRequestJpaRepository

    @Autowired
    private lateinit var apiRequestJpaRepository: ApiRequestJpaRepository

    @Autowired
    private lateinit var apiResponseJpaRepository: ApiResponseJpaRepository

    @BeforeEach
    fun setUp() = runBlocking {
        // Truncate all database tables before testing starts to ensure clean test state
        // Order matters due to foreign key constraints
        println("===============================")
        println("🧹 Cleaning up database tables before test execution...")

        apiResponseJpaRepository.deleteAll()
        apiRequestJpaRepository.deleteAll()
        managePaymentControlsRepository.deleteAll()

        println("✅ Database cleanup completed successfully")
        println("===============================")
    }

    @Test
    @DisplayName("Should execute complete payment controls process flow with real implementations")
    fun `should execute complete payment controls process flow with real implementations`() = runBlocking {
        // Given - Step 1: Create a valid payment controls request
        val inboundRequest = createValidRequest()

        // When - Execute the complete payment controls process flow through real Spring Boot context
        // This uses real implementations of all dependencies:
        // - Real ManagePaymentControlsDomainService
        // - Real ManagePaymentControlsRepositoryAdapter with PostgreSQL database
        // - Real VisaStatusCodeEvaluator
        // - Real VisaApiClientAdapter (will make actual HTTP calls)
        val domainRequest = inboundMapper.toDomain(
            inboundRequest,
            inboundRequest.idempotencyKey,
            inboundRequest.tenantId
        )

        println("🚀 Executing ManagePaymentControls use case with messageId: ${domainRequest.messageId}")
        val result = managePaymentControlsUseCase.managePaymentControls(domainRequest)

        // Then - Step 2: Verify the domain processing results
        println("✅ ManagePaymentControls processing completed")
        println("📊 Result details:")
        println("   - Message ID: ${result.messageId}")
        println("   - Idempotency Key: ${result.idempotencyKey}")
        println("   - Tenant ID: ${result.tenantId}")
        println("   - Account Number: ${result.accountNumber}")
        println("   - Status: ${result.status}")
        println("   - Created Date: ${result.createdDate}")
        println("   - Updated Date: ${result.updatedDate}")

        // Verify basic domain model properties
        assertNotNull(result.id, "Request should have been assigned an ID")
        assertEquals(domainRequest.messageId, result.messageId, "Message ID should be preserved")
        assertEquals(domainRequest.idempotencyKey, result.idempotencyKey, "Idempotency key should be preserved")
        assertEquals(domainRequest.tenantId, result.tenantId, "Tenant ID should be preserved")
        assertEquals(domainRequest.accountNumber, result.accountNumber, "Account number should be preserved")
        assertTrue(result.messageId.startsWith("MPC-"), "Message ID should have MPC- prefix")
        assertEquals(32 + 4, result.messageId.length, "Message ID should be 36 characters total (MPC- + 32 UUID chars)")

        // Verify status progression - should be SUCCESS for successful processing
        assertEquals(VisaRequestStatus.SUCCESS, result.status, "Request should be marked as SUCCESS")
        assertNotNull(result.processedAt, "Processed timestamp should be set for successful requests")

        // Step 3: Verify database persistence
        println("🔍 Verifying database persistence...")
        val savedEntity = managePaymentControlsRepository.findByMessageId(result.messageId)
        assertNotNull(savedEntity, "Request should be persisted in database")
        assertEquals(result.messageId, savedEntity.messageId, "Persisted message ID should match")
        assertEquals(result.status, savedEntity.status, "Persisted status should match")
        assertEquals(result.idempotencyKey, savedEntity.idempotencyKey, "Persisted idempotency key should match")
        assertEquals(result.tenantId, savedEntity.tenantId, "Persisted tenant ID should match")

        // Step 4: Verify JSON data structure preservation
        assertNotNull(savedEntity.paymentControlDetailsData, "Payment control details should be persisted as JSON")
        assertTrue(savedEntity.paymentControlDetailsData.isArray, "Payment control details should be JSON array")
        assertTrue(savedEntity.paymentControlDetailsData.size() > 0, "Payment control details should contain data")

        // Step 5: Verify audit fields
        assertEquals("SYSTEM", savedEntity.createdBy, "Created by should be SYSTEM")
        assertEquals("SYSTEM", savedEntity.updatedBy, "Updated by should be SYSTEM")
        assertNotNull(savedEntity.createdDate, "Created date should be set")
        assertNotNull(savedEntity.updatedDate, "Updated date should be set")

        println("✅ Database persistence verification completed successfully")
        println("===============================")
    }

    @Test
    @DisplayName("Should handle idempotency correctly for duplicate requests")
    fun `should handle idempotency correctly for duplicate requests`() = runBlocking {
        // Given - Create a valid request
        val inboundRequest = createValidRequest()
        val domainRequest = inboundMapper.toDomain(
            inboundRequest,
            inboundRequest.idempotencyKey,
            inboundRequest.tenantId
        )

        // When - Execute the same request twice with same idempotency key
        println("🔄 Testing idempotency with key: ${domainRequest.idempotencyKey}")
        val firstResult = managePaymentControlsUseCase.managePaymentControls(domainRequest)
        val secondResult = managePaymentControlsUseCase.managePaymentControls(domainRequest)

        // Then - Should return the same result for both calls
        assertEquals(firstResult.id, secondResult.id, "Should return same request ID for idempotent calls")
        assertEquals(firstResult.messageId, secondResult.messageId, "Should return same message ID for idempotent calls")
        assertEquals(firstResult.status, secondResult.status, "Should return same status for idempotent calls")

        // Verify only one record exists in database
        val allRequests = managePaymentControlsRepository.findAll()
        val requestsWithSameIdempotencyKey = allRequests.filter { it.idempotencyKey == domainRequest.idempotencyKey }
        assertEquals(1, requestsWithSameIdempotencyKey.size, "Should have only one record for the idempotency key")

        println("✅ Idempotency test completed successfully")
    }

    /**
     * Creates a valid ManagePaymentControls request with realistic payload structure.
     * Uses nested payment control details that match Visa API specification.
     */
    private fun createValidRequest(): ManagePaymentControlsTransactionDto {
        return ManagePaymentControlsTransactionDto(
            idempotencyKey = "test-idempotency-${System.currentTimeMillis()}",
            tenantId = "test-tenant-001",
            accountNumber = "****************",
            paymentControlDetails = listOf(
                PaymentControlDetailDto(
                    rulesSet = listOf(
                        RulesSetDto(
                            action = "R",
                            rules = listOf(
                                RuleDto(
                                    ruleCode = "SPV",
                                    overrides = listOf(
                                        OverrideDto(
                                            sequence = "0",
                                            overrideCode = "endDate",
                                            overrideValue = "12/31/2025"
                                        )
                                    )
                                )
                            )
                        )
                    ),
                    endDate = "2025-12-31",
                    mcgRuleAction = "Block",
                    timeZone = "UTC-0",
                    startDate = "2025-06-12"
                )
            )
        )
    }
}

package com.papertrl.visa.infrastructure.outgoing.dbmanager.entity

import com.fasterxml.jackson.databind.JsonNode
import jakarta.persistence.*
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.type.SqlTypes
import java.time.LocalDateTime

/**
 * JPA Entity for logging outgoing API requests.
 * Stores comprehensive request data for audit trail and monitoring.
 */
@Entity
@Table(name = "api_request")
data class ApiRequestEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    @Column(name = "message_id", length = 100, nullable = false, unique = true)
    val messageId: String,

    @Column(name = "url", length = 500, nullable = false)
    val url: String,

    @Column(name = "http_method", length = 10, nullable = false)
    val httpMethod: String,

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "request_headers")
    val requestHeaders: JsonNode? = null,

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "request_body")
    val requestBody: JsonNode? = null,

    @Column(name = "request_timestamp", nullable = false)
    val requestTimestamp: LocalDateTime = LocalDateTime.now(),

    @Column(name = "error_message", length = 2000)
    val errorMessage: String? = null,

    @Column(name = "request_status", length = 20, nullable = false)
    val requestStatus: String = "SUCCESS",

    @Column(name = "retry_count", nullable = false)
    val retryCount: Int = 0
)

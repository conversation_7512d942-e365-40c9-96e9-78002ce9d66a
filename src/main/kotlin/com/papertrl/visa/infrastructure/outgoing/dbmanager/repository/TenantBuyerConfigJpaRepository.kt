package com.papertrl.visa.infrastructure.outgoing.dbmanager.repository

import com.papertrl.visa.infrastructure.outgoing.dbmanager.entity.TenantBuyerConfigEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

/**
 * JPA Repository interface for TenantBuyerConfigEntity.
 * Provides database operations for tenant buyer configuration entities.
 * Simplified for admin-managed tenant configurations.
 */
@Repository
interface TenantBuyerConfigJpaRepository : JpaRepository<TenantBuyerConfigEntity, Long> {

    /**
     * Find active tenant buyer configuration by tenant ID.
     */
    fun findByTenantIdAndIsActive(tenantId: String, isActive: Boolean = true): TenantBuyerConfigEntity?
}

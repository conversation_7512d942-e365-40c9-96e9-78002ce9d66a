package com.papertrl.visa.infrastructure.outgoing.dbmanager.repository

import com.papertrl.visa.infrastructure.outgoing.dbmanager.entity.ApiRequestEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

/**
 * JPA Repository interface for ApiRequestEntity.
 * Provides database operations for API request entities.
 */
@Repository
interface ApiRequestJpaRepository : JpaRepository<ApiRequestEntity, Long> {

    /**
     * Find an API request by message ID.
     */
    fun findByMessageId(messageId: String): ApiRequestEntity?
}

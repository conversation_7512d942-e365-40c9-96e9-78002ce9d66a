package com.papertrl.visa.infrastructure.outgoing.visa.dto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * DTO for Visa ManagePaymentControls API request.
 * Maps domain model to Visa API expected format.
 * Field order matches exact Visa API specification to prevent API failures.
 * Based on Visa ManagePaymentControls API specification.
 */
data class ManagePaymentControlsRequestDto(
    @JsonProperty("messageId")
    val messageId: String,

    @JsonProperty("clientId")
    val clientId: String,

    @JsonProperty("paymentControlDetails")
    val paymentControlDetails: List<PaymentControlDetailDto>,

    @JsonProperty("accountNumber")
    val accountNumber: String,

    @JsonProperty("buyerId")
    val buyerId: String
)

/**
 * DTO for payment control detail in ManagePaymentControls request.
 */
data class PaymentControlDetailDto(
    @JsonProperty("rulesSet")
    val rulesSet: List<RulesSetDto>,

    @JsonProperty("endDate")
    val endDate: String,

    @JsonProperty("mcgRuleAction")
    val mcgRuleAction: String,

    @JsonProperty("timeZone")
    val timeZone: String,

    @JsonProperty("startDate")
    val startDate: String
)

/**
 * DTO for rules set in payment control detail.
 */
data class RulesSetDto(
    @JsonProperty("action")
    val action: String,

    @JsonProperty("rules")
    val rules: List<RuleDto>
)

/**
 * DTO for rule in rules set.
 */
data class RuleDto(
    @JsonProperty("ruleCode")
    val ruleCode: String,

    @JsonProperty("overrides")
    val overrides: List<OverrideDto>
)

/**
 * DTO for override in rule.
 */
data class OverrideDto(
    @JsonProperty("sequence")
    val sequence: String,

    @JsonProperty("overrideCode")
    val overrideCode: String,

    @JsonProperty("overrideValue")
    val overrideValue: String
)

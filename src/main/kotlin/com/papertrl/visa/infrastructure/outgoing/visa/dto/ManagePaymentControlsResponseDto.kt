package com.papertrl.visa.infrastructure.outgoing.visa.dto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * DTO for Visa ManagePaymentControls API response.
 * Maps the actual Visa API response structure to domain model.
 * Based on actual Visa ManagePaymentControls API response format.
 *
 * Handles both success and error responses with the nested responses.response structure.
 */
data class ManagePaymentControlsResponseDto(
    @JsonProperty("messageId")
    val messageId: String,

    @JsonProperty("responses")
    val responses: ManagePaymentControlsResponsesDto
)

/**
 * DTO for the "responses" object in ManagePaymentControls response.
 * Contains the nested "response" object with actual response details.
 */
data class ManagePaymentControlsResponsesDto(
    @JsonProperty("response")
    val response: ManagePaymentControlsResponseDetailDto
)

/**
 * DTO for the nested "response" object in ManagePaymentControls response.
 * Contains the actual response code and description for both success and error cases.
 *
 * Success Example: responseCode = "00", responseDescription = "Manage Payment Control request processed successfully"
 * Error Example: responseCode = "M002", responseDescription = "Message id is not Unique"
 */
data class ManagePaymentControlsResponseDetailDto(
    @JsonProperty("responseCode")
    val responseCode: String,

    @JsonProperty("responseDescription")
    val responseDescription: String
)

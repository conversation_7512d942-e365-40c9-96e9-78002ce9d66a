package com.papertrl.visa.infrastructure.outgoing.dbmanager.adapter

import com.papertrl.visa.domain.model.entity.TenantBuyerConfig
import com.papertrl.visa.domain.ports.outgoing.dbmanager.TenantBuyerConfigRepository
import com.papertrl.visa.infrastructure.outgoing.dbmanager.entity.TenantBuyerConfigEntity
import com.papertrl.visa.infrastructure.outgoing.dbmanager.repository.TenantBuyerConfigJpaRepository
import org.springframework.stereotype.Component

/**
 * Adapter implementation for TenantBuyerConfigRepository.
 * Bridges domain model with JPA infrastructure.
 * Simplified for admin-managed tenant configurations.
 */
@Component
class TenantBuyerConfigRepositoryAdapter(
    private val jpaRepository: TenantBuyerConfigJpaRepository
) : TenantBuyerConfigRepository {

    override suspend fun findActiveByTenantId(tenantId: String): TenantBuyerConfig? {
        return jpaRepository.findByTenantIdAndIsActive(tenantId, true)?.toDomain()
    }
}

/**
 * Extension function to convert JPA entity to domain model.
 */
fun TenantBuyerConfigEntity.toDomain(): TenantBuyerConfig {
    return TenantBuyerConfig(
        id = this.id,
        tenantId = this.tenantId,
        buyerId = this.buyerId,
        isActive = this.isActive
    )
}

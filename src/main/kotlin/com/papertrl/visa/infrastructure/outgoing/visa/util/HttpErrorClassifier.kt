package com.papertrl.visa.infrastructure.outgoing.visa.util

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.WebClientException
import org.springframework.web.reactive.function.client.WebClientResponseException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.util.concurrent.TimeoutException

/**
 * Utility class to classify HTTP errors and determine retry eligibility.
 * Distinguishes between retryable and non-retryable errors for robust error handling.
 */
@Component
class HttpErrorClassifier {

    private val logger = LoggerFactory.getLogger(HttpErrorClassifier::class.java)

    /**
     * Classification result containing error analysis and retry recommendation.
     */
    data class ErrorClassification(
        val isRetryable: Boolean,
        val errorType: String,
        val description: String,
        val httpStatusCode: Int? = null
    )

    /**
     * Classifies an exception to determine if it should be retried.
     *
     * @param exception The exception to classify
     * @return ErrorClassification with retry recommendation and details
     */
    fun classifyError(exception: Throwable): ErrorClassification {
        logger.debug("Classifying error: ${exception::class.simpleName} - ${exception.message}")

        // First check the root cause for nested exceptions
        val rootCause = findRootCause(exception)
        logger.debug("Root cause: ${rootCause::class.simpleName} - ${rootCause.message}")

        return when {
            // Check root cause first for nested timeout/network errors
            rootCause is SocketTimeoutException || rootCause.javaClass.simpleName.contains("ReadTimeoutException") -> ErrorClassification(
                isRetryable = true,
                errorType = "TIMEOUT",
                description = "Request timeout (root cause: ${rootCause::class.simpleName}): ${rootCause.message ?: exception.message}"
            )

            rootCause is ConnectException -> ErrorClassification(
                isRetryable = true,
                errorType = "NETWORK_CONNECTION",
                description = "Connection refused or network unreachable (root cause: ${rootCause::class.simpleName}): ${rootCause.message ?: exception.message}"
            )

            rootCause is UnknownHostException -> ErrorClassification(
                isRetryable = true,
                errorType = "DNS_RESOLUTION",
                description = "DNS resolution failed for host (root cause: ${rootCause::class.simpleName}): ${rootCause.message ?: exception.message}"
            )

            // Direct exception checks
            exception is ConnectException -> ErrorClassification(
                isRetryable = true,
                errorType = "NETWORK_CONNECTION",
                description = "Connection refused or network unreachable: ${exception.message}"
            )

            exception is UnknownHostException -> ErrorClassification(
                isRetryable = true,
                errorType = "DNS_RESOLUTION",
                description = "DNS resolution failed for host: ${exception.message}"
            )

            // Timeout issues - RETRYABLE
            exception is TimeoutException || exception is SocketTimeoutException -> ErrorClassification(
                isRetryable = true,
                errorType = "TIMEOUT",
                description = "Request timeout: ${exception.message}"
            )

            // WebClient HTTP response exceptions
            exception is WebClientResponseException -> classifyHttpResponseError(exception)

            // General WebClient exceptions - Check cause for better classification
            exception is WebClientException -> classifyWebClientError(exception, rootCause)

            // SSL/Certificate errors - NON-RETRYABLE
            exception is javax.net.ssl.SSLException -> ErrorClassification(
                isRetryable = false,
                errorType = "SSL_ERROR",
                description = "SSL/Certificate error: ${exception.message}"
            )

            // Check for RuntimeException that might wrap other exceptions
            exception is RuntimeException && exception.cause != null -> {
                logger.debug("RuntimeException detected, checking cause: ${exception.cause!!::class.simpleName}")
                classifyError(exception.cause!!)
            }

            // Unknown errors - NON-RETRYABLE (conservative approach)
            else -> ErrorClassification(
                isRetryable = false,
                errorType = "UNKNOWN_ERROR",
                description = "Unknown error type: ${exception::class.simpleName} - ${exception.message}"
            )
        }
    }

    /**
     * Classifies HTTP response errors based on status codes.
     */
    private fun classifyHttpResponseError(exception: WebClientResponseException): ErrorClassification {
        val statusCode = exception.statusCode.value()

        return when (statusCode) {
            // 5xx Server Errors - RETRYABLE
            in 500..599 -> ErrorClassification(
                isRetryable = true,
                errorType = "SERVER_ERROR",
                description = "Server error (${statusCode}): ${exception.message}",
                httpStatusCode = statusCode
            )

            // 429 Rate Limiting - RETRYABLE
            429 -> ErrorClassification(
                isRetryable = true,
                errorType = "RATE_LIMIT",
                description = "Rate limit exceeded (429): ${exception.message}",
                httpStatusCode = statusCode
            )

            // 408 Request Timeout - RETRYABLE
            408 -> ErrorClassification(
                isRetryable = true,
                errorType = "REQUEST_TIMEOUT",
                description = "Request timeout (408): ${exception.message}",
                httpStatusCode = statusCode
            )

            // 401 Unauthorized - NON-RETRYABLE
            401 -> ErrorClassification(
                isRetryable = false,
                errorType = "AUTHENTICATION_ERROR",
                description = "Authentication failed (401): ${exception.message}",
                httpStatusCode = statusCode
            )

            // 403 Forbidden - NON-RETRYABLE
            403 -> ErrorClassification(
                isRetryable = false,
                errorType = "AUTHORIZATION_ERROR",
                description = "Authorization failed (403): ${exception.message}",
                httpStatusCode = statusCode
            )

            // 400 Bad Request - NON-RETRYABLE
            400 -> ErrorClassification(
                isRetryable = false,
                errorType = "BAD_REQUEST",
                description = "Bad request (400): ${exception.message}",
                httpStatusCode = statusCode
            )

            // Other 4xx Client Errors - NON-RETRYABLE
            in 400..499 -> ErrorClassification(
                isRetryable = false,
                errorType = "CLIENT_ERROR",
                description = "Client error (${statusCode}): ${exception.message}",
                httpStatusCode = statusCode
            )

            // Other status codes - NON-RETRYABLE (conservative)
            else -> ErrorClassification(
                isRetryable = false,
                errorType = "HTTP_ERROR",
                description = "HTTP error (${statusCode}): ${exception.message}",
                httpStatusCode = statusCode
            )
        }
    }

    /**
     * Classifies WebClient exceptions by examining the root cause.
     */
    private fun classifyWebClientError(exception: WebClientException, rootCause: Throwable): ErrorClassification {
        return when {
            // Check if root cause indicates a retryable network error
            rootCause is SocketTimeoutException || rootCause.javaClass.simpleName.contains("ReadTimeoutException") -> ErrorClassification(
                isRetryable = true,
                errorType = "TIMEOUT",
                description = "WebClient timeout error (${rootCause::class.simpleName}): ${rootCause.message ?: exception.message}"
            )

            rootCause is ConnectException -> ErrorClassification(
                isRetryable = true,
                errorType = "NETWORK_CONNECTION",
                description = "WebClient connection error (${rootCause::class.simpleName}): ${rootCause.message ?: exception.message}"
            )

            rootCause is UnknownHostException -> ErrorClassification(
                isRetryable = true,
                errorType = "DNS_RESOLUTION",
                description = "WebClient DNS error (${rootCause::class.simpleName}): ${rootCause.message ?: exception.message}"
            )

            // Default WebClient error classification
            else -> ErrorClassification(
                isRetryable = true,
                errorType = "WEBCLIENT_ERROR",
                description = "WebClient error (likely network): ${exception.message} (root cause: ${rootCause::class.simpleName})"
            )
        }
    }

    /**
     * Finds the root cause of an exception by traversing the cause chain.
     */
    private fun findRootCause(exception: Throwable): Throwable {
        var current = exception
        while (current.cause != null && current.cause != current) {
            current = current.cause!!
        }
        return current
    }

    /**
     * Formats error message for database storage (limited to 2000 characters).
     */
    fun formatErrorMessage(classification: ErrorClassification, exception: Throwable): String {
        val baseMessage = "${classification.errorType}: ${classification.description}"
        val stackTrace = exception.stackTrace.take(5).joinToString("\n") { "  at $it" }
        val fullMessage = "$baseMessage\nStack trace:\n$stackTrace"

        return if (fullMessage.length > 2000) {
            fullMessage.take(1997) + "..."
        } else {
            fullMessage
        }
    }
}

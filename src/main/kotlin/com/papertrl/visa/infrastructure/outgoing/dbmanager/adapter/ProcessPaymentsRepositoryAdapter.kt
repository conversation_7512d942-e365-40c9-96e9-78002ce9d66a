package com.papertrl.visa.infrastructure.outgoing.dbmanager.adapter

import com.papertrl.visa.domain.model.entity.ProcessPaymentsRequest
import com.papertrl.visa.domain.model.enums.VisaRequestStatus
import com.papertrl.visa.domain.ports.outgoing.dbmanager.ProcessPaymentsRepository
import com.papertrl.visa.domain.service.TenantBuyerLookupService
import com.papertrl.visa.infrastructure.outgoing.dbmanager.entity.ProcessPaymentsRequestEntity
import com.papertrl.visa.infrastructure.outgoing.dbmanager.repository.ProcessPaymentsRequestJpaRepository
import com.papertrl.visa.infrastructure.outgoing.visa.config.properties.HttpClientProperties
import org.springframework.stereotype.Component
import java.time.LocalDateTime

/**
 * Adapter implementation for ProcessPaymentsRepository.
 * Bridges domain model with JPA infrastructure.
 */
@Component
class ProcessPaymentsRepositoryAdapter(
    private val jpaRepository: ProcessPaymentsRequestJpaRepository,
    private val httpClientProperties: HttpClientProperties,
    private val tenantBuyerLookupService: TenantBuyerLookupService
) : ProcessPaymentsRepository {

    override suspend fun save(request: ProcessPaymentsRequest): ProcessPaymentsRequest {
        val entity = request.toEntity(httpClientProperties, tenantBuyerLookupService)
        val savedEntity = jpaRepository.save(entity)
        return savedEntity.toDomain()
    }

    override suspend fun findByMessageId(messageId: String): ProcessPaymentsRequest? {
        return jpaRepository.findByMessageId(messageId)?.toDomain()
    }

    override suspend fun findByIdempotencyKeyAndStatus(
        idempotencyKey: String,
        status: VisaRequestStatus
    ): ProcessPaymentsRequest? {
        return jpaRepository.findByIdempotencyKeyAndStatus(idempotencyKey, status)?.toDomain()
    }

    override suspend fun findById(id: Long): ProcessPaymentsRequest? {
        return jpaRepository.findById(id).orElse(null)?.toDomain()
    }


    override suspend fun updateStatus(
        messageId: String,
        status: VisaRequestStatus,
        failureReason: String?
    ): ProcessPaymentsRequest? {
        val entity = jpaRepository.findByMessageId(messageId)
        return entity?.let {
            val updatedEntity = it.copy(
                status = status,
                failureReason = failureReason,
                processedAt = if (status == VisaRequestStatus.SUCCESS) LocalDateTime.now() else it.processedAt,
                updatedDate = LocalDateTime.now()
            )
            jpaRepository.save(updatedEntity).toDomain()
        }
    }

    override suspend fun updateVisaResponseFields(
        messageId: String,
        accountNumber: String?,
        expirationDate: String?
    ): ProcessPaymentsRequest? {
        val entity = jpaRepository.findByMessageId(messageId)
        return entity?.let {
            val updatedEntity = it.copy(
                accountNumber = accountNumber,
                expirationDate = expirationDate,
                updatedDate = LocalDateTime.now()
            )
            jpaRepository.save(updatedEntity).toDomain()
        }
    }

    override suspend fun findAll(): List<ProcessPaymentsRequest> {
        return jpaRepository.findAll().map { it.toDomain() }
    }

    override suspend fun deleteAll() {
        jpaRepository.deleteAll()
    }
}

/**
 * Extension function to convert domain model to JPA entity.
 */
suspend fun ProcessPaymentsRequest.toEntity(
    httpClientProperties: HttpClientProperties,
    tenantBuyerLookupService: TenantBuyerLookupService
): ProcessPaymentsRequestEntity {
    val buyerId = tenantBuyerLookupService.getBuyerIdForTenant(this.tenantId)
    return ProcessPaymentsRequestEntity(
        id = this.id,
        messageId = this.messageId,
        idempotencyKey = this.idempotencyKey,
        tenantId = this.tenantId,
        actionType = this.actionType,
        clientId = httpClientProperties.clientId,
        buyerId = buyerId,
        paymentExpiryDate = this.paymentExpiryDate,
        accountType = this.accountType,
        currencyCode = this.currencyCode,
        paymentGrossAmount = this.paymentGrossAmount,
        paymentType = this.paymentType,
        supplierName = this.supplierName,
        supplierID = this.supplierID,
        supplierAddressLine2 = this.supplierAddressLine2,
        supplierAddressLine1 = this.supplierAddressLine1,
        supplierCity = this.supplierCity,
        supplierState = this.supplierState,
        supplierCountryCode = this.supplierCountryCode,
        supplierPostalCode = this.supplierPostalCode,
        primaryEmailAddress = this.primaryEmailAddress,
        emailNotes = this.emailNotes,
        invoicesData = this.invoicesData,
        alternateEmailAddressesData = this.alternateEmailAddressesData,
        status = this.status,
        failureReason = this.failureReason,
        processedAt = this.processedAt,
        accountNumber = this.accountNumber,
        expirationDate = this.expirationDate,
        createdBy = this.createdBy,
        createdDate = this.createdDate,
        updatedBy = this.updatedBy,
        updatedDate = this.updatedDate
    )
}

/**
 * Extension function to convert JPA entity to domain model.
 */
fun ProcessPaymentsRequestEntity.toDomain(): ProcessPaymentsRequest {
    return ProcessPaymentsRequest(
        id = this.id,
        messageId = this.messageId,
        idempotencyKey = this.idempotencyKey,
        tenantId = this.tenantId,
        actionType = this.actionType,
        paymentExpiryDate = this.paymentExpiryDate,
        accountType = this.accountType,
        currencyCode = this.currencyCode,
        paymentGrossAmount = this.paymentGrossAmount,
        paymentType = this.paymentType,
        supplierName = this.supplierName,
        supplierID = this.supplierID,
        supplierAddressLine2 = this.supplierAddressLine2,
        supplierAddressLine1 = this.supplierAddressLine1,
        supplierCity = this.supplierCity,
        supplierState = this.supplierState,
        supplierCountryCode = this.supplierCountryCode,
        supplierPostalCode = this.supplierPostalCode,
        primaryEmailAddress = this.primaryEmailAddress,
        emailNotes = this.emailNotes,
        invoicesData = this.invoicesData,
        alternateEmailAddressesData = this.alternateEmailAddressesData,
        status = this.status,
        failureReason = this.failureReason,
        processedAt = this.processedAt,
        accountNumber = this.accountNumber,
        expirationDate = this.expirationDate,
        createdBy = this.createdBy,
        createdDate = this.createdDate,
        updatedBy = this.updatedBy,
        updatedDate = this.updatedDate
    )
}

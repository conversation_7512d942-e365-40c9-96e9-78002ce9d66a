package com.papertrl.visa.infrastructure.outgoing.dbmanager.entity

import com.fasterxml.jackson.databind.JsonNode
import com.papertrl.visa.domain.model.enums.VisaRequestStatus
import jakarta.persistence.*
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.type.SqlTypes
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * JPA Entity for ProcessPayments requests.
 * Stores comprehensive payment processing data for audit trail and monitoring.
 */
@Entity
@Table(name = "process_payments_request")
data class ProcessPaymentsRequestEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    @Column(name = "message_id", length = 100, nullable = false, unique = true)
    val messageId: String,

    @Column(name = "idempotency_key", length = 255, nullable = false, unique = true)
    val idempotencyKey: String,

    @Column(name = "tenant_id", length = 100, nullable = false)
    val tenantId: String,

    @Column(name = "action_type", nullable = false)
    val actionType: Int,

    @Column(name = "client_id", length = 100, nullable = false)
    val clientId: String,

    @Column(name = "buyer_id", length = 100, nullable = false)
    val buyerId: String,

    @Column(name = "payment_expiry_date", length = 20, nullable = false)
    val paymentExpiryDate: String,

    @Column(name = "account_type", nullable = false)
    val accountType: Int,

    @Column(name = "currency_code", length = 3, nullable = false)
    val currencyCode: String,

    @Column(name = "payment_gross_amount", precision = 19, scale = 2, nullable = false)
    val paymentGrossAmount: BigDecimal,

    @Column(name = "payment_type", length = 50, nullable = false)
    val paymentType: String,

    @Column(name = "supplier_name", length = 255, nullable = false)
    val supplierName: String,

    @Column(name = "supplier_id", nullable = false)
    val supplierID: Long,

    @Column(name = "supplier_address_line2", length = 255)
    val supplierAddressLine2: String? = null,

    @Column(name = "supplier_address_line1", length = 255, nullable = false)
    val supplierAddressLine1: String,

    @Column(name = "supplier_city", length = 100, nullable = false)
    val supplierCity: String,

    @Column(name = "supplier_state", length = 50, nullable = false)
    val supplierState: String,

    @Column(name = "supplier_country_code", length = 3, nullable = false)
    val supplierCountryCode: String,

    @Column(name = "supplier_postal_code", length = 20, nullable = false)
    val supplierPostalCode: String,

    @Column(name = "primary_email_address", length = 255, nullable = false)
    val primaryEmailAddress: String,

    @Column(name = "email_notes", length = 1000)
    val emailNotes: String? = null,

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "invoices_data", nullable = false)
    val invoicesData: JsonNode, // JSON representation of invoices

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "alternate_email_addresses_data")
    val alternateEmailAddressesData: JsonNode? = null, // JSON representation

    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 20, nullable = false)
    val status: VisaRequestStatus = VisaRequestStatus.PENDING,

    @Column(name = "failure_reason", length = 1000)
    val failureReason: String? = null,

    @Column(name = "processed_at")
    val processedAt: LocalDateTime? = null,

    @Column(name = "account_number", length = 50)
    val accountNumber: String? = null,

    @Column(name = "expiration_date", length = 20)
    val expirationDate: String? = null,

    @Column(name = "created_by", length = 100, nullable = false)
    val createdBy: String = "SYSTEM",

    @Column(name = "created_date", nullable = false)
    val createdDate: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_by", length = 100, nullable = false)
    val updatedBy: String = "SYSTEM",

    @Column(name = "updated_date", nullable = false)
    val updatedDate: LocalDateTime = LocalDateTime.now()
)

package com.papertrl.visa.infrastructure.outgoing.visa.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal

/**
 * DTO for payment wrapper object in ProcessPayments request.
 * Contains payment-related fields that are nested under "payment" in VISA API.
 * Based on VISA ProcessPayments API specification.
 */
data class PaymentDto(
    @JsonProperty("paymentExpiryDate")
    val paymentExpiryDate: String,

    @JsonProperty("accountType")
    val accountType: Int,

    @JsonProperty("currencyCode")
    val currencyCode: String,

    @JsonProperty("paymentGrossAmount")
    val paymentGrossAmount: BigDecimal,

    @JsonProperty("paymentType")
    val paymentType: String,

    @JsonProperty("invoices")
    val invoices: List<InvoiceDto>,

    @JsonProperty("supplier")
    val supplier: SupplierDto
)

package com.papertrl.visa.infrastructure.outgoing.visa.config

import com.papertrl.visa.domain.model.dto.ResponseData
import com.papertrl.visa.domain.ports.incoming.dbmanager.ApiLoggingUseCase
import com.papertrl.visa.infrastructure.outgoing.visa.config.properties.HttpClientProperties
import com.papertrl.visa.infrastructure.outgoing.visa.config.properties.SslProperties
import io.netty.channel.ChannelOption
import io.netty.handler.ssl.SslContext
import io.netty.handler.timeout.ReadTimeoutHandler
import io.netty.handler.timeout.WriteTimeoutHandler
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.io.buffer.DataBufferUtils
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.client.reactive.ReactorClientHttpConnector
import org.springframework.web.reactive.function.client.ClientRequest
import org.springframework.web.reactive.function.client.ExchangeFilterFunction
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient
import reactor.core.publisher.Mono
import reactor.netty.http.client.HttpClient
import java.nio.charset.StandardCharsets
import java.time.Duration
import java.time.LocalDateTime
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * WebClient configuration for HTTP client infrastructure.
 * Provides timeout handling, connection pooling, and request/response processing.
 */
@Configuration
@EnableConfigurationProperties(HttpClientProperties::class, SslProperties::class)
class WebClientConfig(
    private val httpClientProperties: HttpClientProperties,
    private val sslProperties: SslProperties,
    private val apiLoggingUseCase: ApiLoggingUseCase,
    @Qualifier("visaSslContext") private val sslContext: SslContext?
) {

    private val logger = LoggerFactory.getLogger(WebClientConfig::class.java)

    /**
     * Creates configured WebClient builder with timeout handling and filters.
     */
    @Bean
    fun webClientBuilder(): WebClient.Builder {
        val httpClient = createHttpClient()

        val exchangeStrategies = ExchangeStrategies.builder()
            .codecs { configurer ->
                configurer.defaultCodecs().maxInMemorySize(10240 * 2)
            }
            .build()

        return WebClient.builder()
            .clientConnector(ReactorClientHttpConnector(httpClient))
            .exchangeStrategies(exchangeStrategies)
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.USER_AGENT, "Visa-Adaptor/1.0")
            .filter(visaBasicAuthFilter())
            // .filter(createRequestResponseLoggingFilter()) // ❌ DISABLED: Replaced by ReactiveHttpClient error handling
            .filter(errorHandlingFilter())
    }

    /**
     * Creates configured WebClient for Visa API communication.
     */
    @Bean("visaApiWebClient")
    fun visaApiWebClient(): WebClient {
        return webClientBuilder()
            .baseUrl(httpClientProperties.baseUrl)
            .build()
    }

    /**
     * Creates HTTP client with timeout, connection pool, and SSL configuration.
     */
    private fun createHttpClient(): HttpClient {
        val baseHttpClient = HttpClient.create()
            .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, httpClientProperties.connectionTimeout.toInt())
            .responseTimeout(Duration.ofMillis(httpClientProperties.timeout))
            .doOnConnected { conn ->
                conn.addHandlerLast(ReadTimeoutHandler(httpClientProperties.readTimeout, TimeUnit.MILLISECONDS))
                    .addHandlerLast(WriteTimeoutHandler(httpClientProperties.writeTimeout, TimeUnit.MILLISECONDS))
            }

        // Configure Visa-compliant SSL if enabled and SSL context is available
        logger.info("SSL Configuration Status:")
        logger.info("  - SSL Enabled: ${sslProperties.enabled}")
        logger.info("  - SSL Context Available: ${sslContext != null}")
        logger.info("  - SSL Context Type: ${sslContext?.javaClass?.simpleName ?: "null"}")

        return if (sslProperties.enabled && sslContext != null) {
            logger.info("✅ Configuring WebClient with Visa-compliant SSL/TLS support")
            logger.info("SSL Protocol: ${sslProperties.protocol}")

            baseHttpClient.secure { sslSpec ->
                sslSpec.sslContext(sslContext)
                logger.info("✅ SSL context configured with Visa-compliant settings")
            }
        } else {
            if (sslProperties.enabled && sslContext == null) {
                logger.error("❌ SSL is enabled but SSL context is null - this will cause PKIX errors!")
                logger.error("Check SslConfig bean creation and qualifier matching")
            } else if (!sslProperties.enabled) {
                logger.info("SSL is disabled - configuring WebClient without SSL/TLS support")
            }
            baseHttpClient
        }
    }

    /**
     * Visa Basic Authentication Filter.
     */
    private fun visaBasicAuthFilter(): ExchangeFilterFunction {
        return ExchangeFilterFunction.ofRequestProcessor { request ->
            if (httpClientProperties.userId.isNotBlank() && httpClientProperties.password.isNotBlank()) {
                logger.debug("Adding Visa basic authentication for: {}", request.url())

                val auth = "${httpClientProperties.userId}:${httpClientProperties.password}"
                val encodedAuth = Base64.getEncoder().encode(auth.toByteArray(StandardCharsets.UTF_8))
                val authHeaderValue = "Basic ${String(encodedAuth)}"

                val authenticatedRequest = ClientRequest.from(request)
                    .header("Authorization", authHeaderValue)
                    .build()

                Mono.just(authenticatedRequest)
            } else {
                logger.warn(
                    "Visa API credentials not configured - userId: '{}', password: '{}'",
                    httpClientProperties.userId,
                    if (httpClientProperties.password.isBlank()) "BLANK" else "***"
                )
                Mono.just(request)
            }
        }
    }

    /**
     * Error handling filter for consistent error responses.
     * Now properly captures error response bodies without consuming the stream
     * and saves error responses to the api_response table.
     */
    private fun errorHandlingFilter(): ExchangeFilterFunction {
        return ExchangeFilterFunction.ofResponseProcessor { response ->
            if (response.statusCode().isError) {
                // Extract message ID from response headers or context
                val messageId = response.headers().asHttpHeaders().getFirst("X-Request-ID")
                    ?: "ERROR-${System.currentTimeMillis()}"

                // Use the same proven pattern as RequestResponseLoggingFilter
                response.mutate()
                    .body { originalBodyFlux ->
                        DataBufferUtils.join(originalBodyFlux)
                            .doOnNext { dataBuffer ->
                                // Read the error response body for logging
                                val errorBody = try {
                                    val bytes = ByteArray(dataBuffer.readableByteCount())
                                    dataBuffer.read(bytes)
                                    // Reset the buffer position for downstream consumption
                                    dataBuffer.readPosition(0)
                                    String(bytes, StandardCharsets.UTF_8)
                                } catch (e: Exception) {
                                    logger.warn("Failed to read error response body: ${e.message}")
                                    "[Error body read failed: ${e.message}]"
                                }

                                // Log to console
                                logger.error(
                                    "HTTP error response: Status={}, Body={}",
                                    response.statusCode(),
                                    errorBody
                                )

                                // Save error response to database
                                try {
                                    runBlocking {
                                        val responseData = ResponseData(
                                            messageId = messageId,
                                            statusCode = response.statusCode().value(),
                                            headers = response.headers().asHttpHeaders().toSingleValueMap(),
                                            body = errorBody,
                                            timestamp = LocalDateTime.now()
                                        )
                                        apiLoggingUseCase.updateWithResponse(messageId, responseData)
                                    }
                                    logger.debug("Error response saved to database for messageId: $messageId")
                                } catch (e: Exception) {
                                    logger.error(
                                        "Failed to save error response to database for messageId: $messageId",
                                        e
                                    )
                                }
                            }
                            .flux()
                            .onErrorResume { error ->
                                logger.error("Failed to capture error response body for messageId: $messageId", error)
                                originalBodyFlux
                            }
                    }
                    .build()
                    .let { Mono.just(it) }
            } else {
                Mono.just(response)
            }
        }
    }


}

package com.papertrl.visa.infrastructure.outgoing.visa.mapper

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.papertrl.visa.domain.model.entity.ManagePaymentControlsRequest
import com.papertrl.visa.domain.service.TenantBuyerLookupService
import com.papertrl.visa.infrastructure.outgoing.visa.config.properties.HttpClientProperties
import com.papertrl.visa.infrastructure.outgoing.visa.dto.ManagePaymentControlsRequestDto
import com.papertrl.visa.infrastructure.outgoing.visa.dto.PaymentControlDetailDto
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Mapper for converting between ManagePaymentControls domain model and Visa API DTOs.
 * Handles JSON parsing and data transformation for Visa API integration.
 */
@Component
class ManagePaymentControlsMapper(
    private val objectMapper: ObjectMapper,
    private val httpClientProperties: HttpClientProperties,
    private val tenantBuyerLookupService: TenantBuyerLookupService
) {

    private val logger = LoggerFactory.getLogger(ManagePaymentControlsMapper::class.java)

    /**
     * Convert domain model to Visa API request DTO.
     * Uses tenant-specific buyer ID lookup for dynamic buyer ID assignment.
     */
    suspend fun toRequestDto(
        domainRequest: ManagePaymentControlsRequest,
        paymentControlDetails: List<PaymentControlDetailDto>
    ): ManagePaymentControlsRequestDto {
        logger.debug("Converting domain request to DTO for messageId: ${domainRequest.messageId}, tenantId: ${domainRequest.tenantId}")

        val buyerId = tenantBuyerLookupService.getBuyerIdForTenant(domainRequest.tenantId)
        logger.debug("Retrieved buyer ID: $buyerId for tenant: ${domainRequest.tenantId}")

        return ManagePaymentControlsRequestDto(
            messageId = domainRequest.messageId,
            clientId = httpClientProperties.clientId,
            paymentControlDetails = paymentControlDetails,
            accountNumber = domainRequest.accountNumber,
            buyerId = buyerId
        )
    }


    /**
     * Parse payment control details from JsonNode to DTO list.
     */
    private fun parsePaymentControlDetails(paymentControlDetailsData: JsonNode): List<PaymentControlDetailDto> {
        return try {
            objectMapper.convertValue(
                paymentControlDetailsData,
                object : TypeReference<List<PaymentControlDetailDto>>() {}
            )
        } catch (e: Exception) {
            logger.error("Failed to parse payment control details: ${e.message}", e)
            throw RuntimeException("Invalid payment control details format: ${e.message}", e)
        }
    }
}

package com.papertrl.visa.infrastructure.outgoing.dbmanager.repository

import com.papertrl.visa.infrastructure.outgoing.dbmanager.entity.ApiResponseEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

/**
 * JPA Repository interface for ApiResponseEntity.
 * Provides database operations for API response entities.
 */
@Repository
interface ApiResponseJpaRepository : JpaRepository<ApiResponseEntity, Long> {

    /**
     * Find API responses by request ID.
     */
    fun findByApiRequestId(apiRequestId: Long): List<ApiResponseEntity>


}

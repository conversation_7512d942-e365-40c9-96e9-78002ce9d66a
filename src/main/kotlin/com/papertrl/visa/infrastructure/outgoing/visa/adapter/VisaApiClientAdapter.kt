package com.papertrl.visa.infrastructure.outgoing.visa.adapter

import com.fasterxml.jackson.databind.ObjectMapper
import com.papertrl.visa.domain.model.entity.GetPaymentControlsRequest
import com.papertrl.visa.domain.model.entity.ManagePaymentControlsRequest
import com.papertrl.visa.domain.model.entity.ProcessPaymentsRequest
import com.papertrl.visa.domain.ports.outgoing.dbmanager.ApiRequestRepository
import com.papertrl.visa.domain.ports.outgoing.dbmanager.ProcessPaymentsRepository
import com.papertrl.visa.domain.ports.outgoing.visa.VisaApiClient
import com.papertrl.visa.infrastructure.outgoing.visa.client.ReactiveHttpClient
import com.papertrl.visa.infrastructure.outgoing.visa.config.properties.HttpClientProperties
import com.papertrl.visa.infrastructure.outgoing.visa.dto.GetPaymentControlsResponseDto
import com.papertrl.visa.infrastructure.outgoing.visa.dto.ManagePaymentControlsResponseDto
import com.papertrl.visa.infrastructure.outgoing.visa.dto.ProcessPaymentsResponseDto
import com.papertrl.visa.infrastructure.outgoing.visa.evaluator.VisaStatusCodeEvaluator
import com.papertrl.visa.infrastructure.outgoing.visa.mapper.GetPaymentControlsMapper
import com.papertrl.visa.infrastructure.outgoing.visa.mapper.ManagePaymentControlsMapper
import com.papertrl.visa.infrastructure.outgoing.visa.mapper.ProcessPaymentsMapper
import com.papertrl.visa.infrastructure.outgoing.visa.util.HttpErrorClassifier
import kotlinx.coroutines.delay
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import kotlin.math.min
import kotlin.math.pow

/**
 * Adapter implementation for VisaApiClient.
 * Handles external Visa API integration for ProcessPayments.
 * Converts domain model to DTO, calls Visa API, and returns updated domain model.
 */
@Component
class VisaApiClientAdapter(
    private val reactiveHttpClient: ReactiveHttpClient,
    private val processPaymentsMapper: ProcessPaymentsMapper,
    private val managePaymentControlsMapper: ManagePaymentControlsMapper,
    private val getPaymentControlsMapper: GetPaymentControlsMapper,
    private val visaStatusCodeEvaluator: VisaStatusCodeEvaluator,
    private val httpClientProperties: HttpClientProperties,
    private val apiRequestRepository: ApiRequestRepository,
    private val httpErrorClassifier: HttpErrorClassifier,
    private val objectMapper: ObjectMapper,
    private val processPaymentsRepository: ProcessPaymentsRepository,
) : VisaApiClient {

    private val logger = LoggerFactory.getLogger(VisaApiClientAdapter::class.java)

    override suspend fun sendProcessPaymentsRequest(request: ProcessPaymentsRequest): ProcessPaymentsRequest {
        logger.info("Sending ProcessPayments request to Visa API for message ID: ${request.messageId}")

        val maxAttempts = httpClientProperties.retry.maxAttempts
        val initialDelay = httpClientProperties.retry.initialDelay
        val maxDelay = httpClientProperties.retry.maxDelay
        val multiplier = httpClientProperties.retry.multiplier

        var lastException: Exception? = null
        var currentApiRequestId: Long? = null
        var attempt = 1

        while (attempt <= maxAttempts) {
            try {
                logger.debug(
                    "Attempt {}/{} for ProcessPayments request messageId: {}",
                    attempt,
                    maxAttempts,
                    request.messageId
                )

                // Step 1: Convert domain model to Visa API DTO with tenant-specific buyer ID
                val requestDto = processPaymentsMapper.toRequestDto(request)
                logger.debug("Converted domain request to DTO for messageId: ${request.messageId}")

                // Step 2: Make HTTP call to Visa API and get response DTO directly
                val processPaymentsEndpoint = httpClientProperties.findEndpointByName("process-payments")?.path
                    ?: throw IllegalStateException("ProcessPayments endpoint not configured")
                val responseDto = reactiveHttpClient.post(
                    url = processPaymentsEndpoint,
                    data = requestDto,
                    responseType = ProcessPaymentsResponseDto::class.java,
                    messageId = request.messageId
                )

                logger.debug("Received Visa API response for messageId: ${request.messageId}, statusCode: ${responseDto.statusCode}")

                // Step 3: Evaluate Visa API status code
                if (!visaStatusCodeEvaluator.isProcessPaymentsSuccess(responseDto.statusCode)) {
                    val errorMessage = visaStatusCodeEvaluator.getProcessPaymentsErrorMessage(responseDto.statusCode)
                    logger.error("Visa API business error for messageId: ${request.messageId} - statusCode: ${responseDto.statusCode}, message: $errorMessage")

                    // Check if it's a retryable error
                    if (visaStatusCodeEvaluator.isProcessPaymentsRetryable(responseDto.statusCode)) {
                        throw RuntimeException("Visa API retryable error: $errorMessage (statusCode: ${responseDto.statusCode})")
                    } else {
                        throw RuntimeException("Visa API permanent error: $errorMessage (statusCode: ${responseDto.statusCode})")
                    }
                }

                // Step 4: Update final success status if we had retries
                currentApiRequestId?.let { updateApiRequestStatus(it, "SUCCESS", null) }

                // Step 5: Extract and save VISA response fields
                try {
                    val updatedRequest = processPaymentsRepository.updateVisaResponseFields(
                        messageId = request.messageId,
                        accountNumber = responseDto.accountNumber,
                        expirationDate = responseDto.expirationDate
                    )
                    logger.info("Successfully processed ProcessPayments request for messageId: ${request.messageId}, accountNumber: ${responseDto.accountNumber}, expirationDate: ${responseDto.expirationDate}")
                    return updatedRequest ?: request
                } catch (e: Exception) {
                    logger.warn("Failed to update VISA response fields for messageId: ${request.messageId}: ${e.message}")
                    // Don't fail the main transaction - return original request
                    return request
                }

            } catch (e: Exception) {
                lastException = e
                logger.warn(
                    "Attempt {}/{} failed for ProcessPayments request messageId: {}: {}",
                    attempt, maxAttempts, request.messageId, e.message
                )

                // Classify error to determine if retry is appropriate
                val classification = httpErrorClassifier.classifyError(e)

                // Update retry status first (before checking if we should break)
                if (currentApiRequestId == null) {
                    // If we don't have an API request ID yet, try to find or create one
                    currentApiRequestId = findOrCreateApiRequestId(request.messageId)
                }

                // Update retry status for this attempt
                currentApiRequestId?.let {
                    updateApiRequestRetryStatus(it, attempt, classification.description)
                }

                // Check if we should stop retrying
                if (!classification.isRetryable || attempt == maxAttempts) {
                    logger.error("Non-retryable error or max attempts reached for messageId: ${request.messageId}, errorType: ${classification.errorType}")
                    currentApiRequestId?.let { updateApiRequestStatus(it, "FAILED", classification.description) }
                    break
                }

                // Wait before next attempt (only if we're going to retry)
                val delayMs = calculateDelay(attempt, initialDelay, maxDelay, multiplier)
                logger.info(
                    "Retrying in {}ms for messageId: {}, attempt: {}/{}",
                    delayMs,
                    request.messageId,
                    attempt + 1,
                    maxAttempts
                )
                delay(delayMs)
            }

            // Increment attempt counter for next iteration
            attempt++
        }

        // All attempts failed
        logger.error("All {} attempts failed for ProcessPayments request messageId: {}", maxAttempts, request.messageId)
        throw RuntimeException(
            "ProcessPayments API call failed after $maxAttempts attempts: ${lastException?.message}",
            lastException
        )
    }

    @Deprecated("Use sendManagePaymentControlsRequest(request, paymentControlDetails) instead")
    override suspend fun sendManagePaymentControlsRequest(request: ManagePaymentControlsRequest): ManagePaymentControlsRequest {
        throw UnsupportedOperationException("Use sendManagePaymentControlsRequest(request, paymentControlDetails) instead")
    }

    override suspend fun sendManagePaymentControlsRequest(
        request: ManagePaymentControlsRequest,
        paymentControlDetails: List<com.papertrl.visa.infrastructure.outgoing.visa.dto.PaymentControlDetailDto>
    ): ManagePaymentControlsRequest {
        logger.info("Sending ManagePaymentControls request to Visa API for message ID: ${request.messageId}")

        val maxAttempts = httpClientProperties.retry.maxAttempts
        val initialDelay = httpClientProperties.retry.initialDelay
        val maxDelay = httpClientProperties.retry.maxDelay
        val multiplier = httpClientProperties.retry.multiplier

        var lastException: Exception? = null
        var currentApiRequestId: Long? = null
        var attempt = 1

        while (attempt <= maxAttempts) {
            try {
                // Step 1: Convert domain to DTO
                val requestDto = managePaymentControlsMapper.toRequestDto(request, paymentControlDetails)

                // Step 2: Make HTTP call to Visa API and get response DTO directly
                val managePaymentControlsEndpoint =
                    httpClientProperties.findEndpointByName("manage-payment-controls")?.path
                        ?: throw IllegalStateException("ManagePaymentControls endpoint not configured")
                val responseDto = reactiveHttpClient.post(
                    url = managePaymentControlsEndpoint,
                    data = requestDto,
                    responseType = ManagePaymentControlsResponseDto::class.java,
                    messageId = request.messageId
                )

                // Extract response code from the nested structure
                val responseCode = responseDto.responses.response.responseCode
                val responseDescription = responseDto.responses.response.responseDescription

                logger.debug("Received Visa API response for messageId: ${request.messageId}, responseCode: $responseCode, description: $responseDescription")

                // Step 3: Evaluate Visa API response code
                if (!visaStatusCodeEvaluator.isManagePaymentControlsSuccess(responseCode)) {
                    val errorMessage = visaStatusCodeEvaluator.getManagePaymentControlsErrorMessage(responseCode)
                    logger.error("Visa API business error for messageId: ${request.messageId} - responseCode: $responseCode, message: $errorMessage, description: $responseDescription")

                    // Check if it's a retryable error
                    if (visaStatusCodeEvaluator.isManagePaymentControlsRetryable(responseCode)) {
                        throw RuntimeException("Visa API retryable error: $errorMessage (responseCode: $responseCode)")
                    } else {
                        throw RuntimeException("Visa API permanent error: $errorMessage (responseCode: $responseCode)")
                    }
                }

                // Step 4: Update final success status if we had retries
                currentApiRequestId?.let { updateApiRequestStatus(it, "SUCCESS", null) }

                logger.info("Successfully processed ManagePaymentControls request for messageId: ${request.messageId}")
                return request

            } catch (e: Exception) {
                lastException = e
                logger.warn(
                    "Attempt {}/{} failed for ManagePaymentControls request messageId: {}: {}",
                    attempt, maxAttempts, request.messageId, e.message
                )

                // Classify error to determine if retry is appropriate
                val classification = httpErrorClassifier.classifyError(e)

                // Update retry status first (before checking if we should break)
                if (currentApiRequestId == null) {
                    // If we don't have an API request ID yet, try to find or create one
                    currentApiRequestId = findOrCreateApiRequestId(request.messageId)
                }

                // Update retry status for this attempt
                currentApiRequestId?.let {
                    updateApiRequestRetryStatus(it, attempt, classification.description)
                }

                // Check if we should stop retrying
                if (!classification.isRetryable || attempt == maxAttempts) {
                    logger.error("Non-retryable error or max attempts reached for messageId: ${request.messageId}, errorType: ${classification.errorType}")
                    currentApiRequestId?.let { updateApiRequestStatus(it, "FAILED", classification.description) }
                    break
                }

                // Wait before next attempt (only if we're going to retry)
                val delayMs = calculateDelay(attempt, initialDelay, maxDelay, multiplier)
                logger.info(
                    "Retrying in {}ms for messageId: {}, attempt: {}/{}",
                    delayMs,
                    request.messageId,
                    attempt + 1,
                    maxAttempts
                )
                delay(delayMs)
            }

            // Increment attempt counter for next iteration
            attempt++
        }

        // All attempts failed
        logger.error(
            "All {} attempts failed for ManagePaymentControls request messageId: {}",
            maxAttempts,
            request.messageId
        )
        throw RuntimeException(
            "ManagePaymentControls API call failed after $maxAttempts attempts: ${lastException?.message}",
            lastException
        )
    }

    /**
     * Calculates exponential backoff delay for retry attempts.
     */
    private fun calculateDelay(attempt: Int, initialDelay: Long, maxDelay: Long, multiplier: Double): Long {
        val exponentialDelay = (initialDelay * multiplier.pow(attempt - 1)).toLong()
        return min(exponentialDelay, maxDelay)
    }

    /**
     * Finds or creates an API request ID for error tracking.
     */
    private suspend fun findOrCreateApiRequestId(messageId: String?): Long? {
        return try {
            messageId?.let {
                // Try to find existing request first
                val existingRequest = apiRequestRepository.findByMessageId(it)
                existingRequest?.id ?: run {
                    // Create a new request entry if not found
                    logger.debug("Creating new API request entry for messageId: {}", messageId)
                    // Note: This is a fallback - normally the request should already exist
                    null
                }
            }
        } catch (e: Exception) {
            logger.warn("Failed to find/create API request for messageId: {}: {}", messageId, e.message)
            null
        }
    }

    /**
     * Updates API request retry status during retry attempts.
     */
    private suspend fun updateApiRequestRetryStatus(apiRequestId: Long, retryCount: Int, errorMessage: String) {
        try {
            val existingRequest = apiRequestRepository.findById(apiRequestId)
            existingRequest?.let { request ->
                val updatedRequest = request.copy(
                    requestStatus = "RETRYING",
                    retryCount = retryCount,
                    errorMessage = errorMessage
                )
                apiRequestRepository.save(updatedRequest)
                logger.debug("Updated retry status for apiRequestId: {}, retryCount: {}", apiRequestId, retryCount)
            }
        } catch (e: Exception) {
            logger.warn("Failed to update retry status for apiRequestId: {}: {}", apiRequestId, e.message)
        }
    }

    /**
     * Updates final API request status after all attempts.
     */
    private suspend fun updateApiRequestStatus(apiRequestId: Long, status: String, errorMessage: String?) {
        try {
            val existingRequest = apiRequestRepository.findById(apiRequestId)
            existingRequest?.let { request ->
                val updatedRequest = request.copy(
                    requestStatus = status,
                    errorMessage = errorMessage ?: request.errorMessage
                )
                apiRequestRepository.save(updatedRequest)
                logger.debug("Updated final status for apiRequestId: {}, status: {}", apiRequestId, status)
            }
        } catch (e: Exception) {
            logger.warn("Failed to update final status for apiRequestId: {}: {}", apiRequestId, e.message)
        }
    }

    override suspend fun sendGetPaymentControlsRequest(request: GetPaymentControlsRequest): GetPaymentControlsRequest {
        logger.info("Sending GetPaymentControls request to Visa API for message ID: ${request.messageId}")

        val maxAttempts = httpClientProperties.retry.maxAttempts
        val initialDelay = httpClientProperties.retry.initialDelay
        val maxDelay = httpClientProperties.retry.maxDelay
        val multiplier = httpClientProperties.retry.multiplier

        var lastException: Exception? = null
        var attempt = 1

        while (attempt <= maxAttempts) {
            try {
                // Convert domain to DTO
                val requestDto = getPaymentControlsMapper.toRequestDto(request)

                // Make HTTP call to Visa API and get response DTO directly
                val getPaymentControlsEndpoint = httpClientProperties.findEndpointByName("get-payment-controls")?.path
                    ?: throw IllegalStateException("GetPaymentControls endpoint not configured")
                val responseDto = reactiveHttpClient.post(
                    url = getPaymentControlsEndpoint,
                    data = requestDto,
                    responseType = GetPaymentControlsResponseDto::class.java,
                    messageId = request.messageId
                )

                logger.debug("Received Visa API response for messageId: ${request.messageId}, statusCode: ${responseDto.statusCode}")

                // Evaluate Visa API status code
                if (!visaStatusCodeEvaluator.isGetPaymentControlsSuccess(responseDto.statusCode)) {
                    val errorMessage = visaStatusCodeEvaluator.getGetPaymentControlsErrorMessage(responseDto.statusCode)
                    logger.error("Visa API business error for messageId: ${request.messageId} - statusCode: ${responseDto.statusCode}, message: $errorMessage")

                    // Check if it's a retryable error
                    if (visaStatusCodeEvaluator.isGetPaymentControlsRetryable(responseDto.statusCode)) {
                        throw RuntimeException("Visa API retryable error: $errorMessage (statusCode: ${responseDto.statusCode})")
                    } else {
                        throw RuntimeException("Visa API permanent error: $errorMessage (statusCode: ${responseDto.statusCode})")
                    }
                }

                // Store retrieved payment controls data in the request
                // Since the new DTO structure has rules directly in the response, store the entire response
                val retrievedData = objectMapper.valueToTree<com.fasterxml.jackson.databind.JsonNode>(responseDto)

                val updatedRequest = request.withRetrievedData(retrievedData)

                logger.info("Successfully processed GetPaymentControls request for messageId: ${request.messageId}")
                return updatedRequest

            } catch (e: Exception) {
                lastException = e
                logger.warn(
                    "Attempt {}/{} failed for GetPaymentControls request messageId: {}: {}",
                    attempt, maxAttempts, request.messageId, e.message
                )

                // Classify error to determine if retry is appropriate
                val classification = httpErrorClassifier.classifyError(e)

                // Check if we should stop retrying
                if (!classification.isRetryable || attempt == maxAttempts) {
                    logger.error("Non-retryable error or max attempts reached for messageId: ${request.messageId}, errorType: ${classification.errorType}")
                    break
                }

                // Wait before next attempt (only if we're going to retry)
                val delayMs = calculateDelay(attempt, initialDelay, maxDelay, multiplier)
                logger.info(
                    "Retrying in {}ms for messageId: {}, attempt: {}/{}",
                    delayMs,
                    request.messageId,
                    attempt + 1,
                    maxAttempts
                )
                delay(delayMs)
            }

            // Increment attempt counter for next iteration
            attempt++
        }

        // All attempts failed
        logger.error(
            "All {} attempts failed for GetPaymentControls request messageId: {}",
            maxAttempts,
            request.messageId
        )
        throw RuntimeException(
            "GetPaymentControls API call failed after $maxAttempts attempts: ${lastException?.message}",
            lastException
        )
    }
}

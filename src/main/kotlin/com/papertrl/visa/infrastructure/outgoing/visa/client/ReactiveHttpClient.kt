package com.papertrl.visa.infrastructure.outgoing.visa.client

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.papertrl.visa.domain.model.entity.ApiRequest
import com.papertrl.visa.domain.model.entity.ApiResponse
import com.papertrl.visa.domain.ports.outgoing.dbmanager.ApiRequestRepository
import com.papertrl.visa.domain.ports.outgoing.dbmanager.ApiResponseRepository
import com.papertrl.visa.infrastructure.outgoing.visa.util.HttpErrorClassifier
import kotlinx.coroutines.reactive.awaitSingle
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.WebClientResponseException
import java.time.LocalDateTime
import java.util.*

/**
 * Reactive HTTP client for external API communication.
 * Response logging is now handled by the RequestResponseLoggingFilter.
 */
@Component
class ReactiveHttpClient(
    @Qualifier("visaApiWebClient") private val webClient: WebClient,
    private val objectMapper: ObjectMapper,
    private val apiRequestRepository: ApiRequestRepository,
    private val apiResponseRepository: ApiResponseRepository,
    private val httpErrorClassifier: HttpErrorClassifier
) {

    private val logger = LoggerFactory.getLogger(ReactiveHttpClient::class.java)

    /**
     * Performs HTTP GET request and returns the deserialized response DTO.
     * Includes comprehensive error handling and database logging.
     */
    suspend fun <T> get(
        url: String,
        responseType: Class<T>,
        messageId: String? = null
    ): T {
        val actualMessageId = messageId ?: generateMessageId()
        logger.debug("Executing GET request to {} with messageId: {}", url, actualMessageId)

        return try {
            // First, log the request as it's being made
            val savedRequest = logRequestAttempt(actualMessageId, url, "GET", null)

            val result = webClient
                .get()
                .uri(url)
                .header("X-Request-ID", actualMessageId)
                .retrieve()
                .bodyToMono(responseType)
                .awaitSingle()

            // Log successful request and response
            logSuccessfulResponse(savedRequest.id!!, 200, result)
            updateRequestStatus(savedRequest.id, "SUCCESS", null)
            result

        } catch (exception: Exception) {
            logger.error(
                "Error during GET request to {} with messageId {}: {}",
                url, actualMessageId, exception.message, exception
            )

            // Log failed request and response (if available)
            val savedRequest = findOrCreateFailedRequest(actualMessageId, url, "GET", null, exception)
            logFailedResponse(savedRequest.id!!, exception)

            // Re-throw the original exception to preserve error classification
            throw exception
        }
    }

    /**
     * Performs HTTP POST request and returns the deserialized response DTO.
     * Includes comprehensive error handling and database logging.
     */
    suspend fun <T> post(
        url: String,
        data: Any,
        responseType: Class<T>,
        messageId: String? = null
    ): T {
        val actualMessageId = messageId
        logger.debug("Executing POST request to {} with messageId: {}", url, actualMessageId)

        try {
            // First, log the request as it's being made
            val savedRequest = logRequestAttempt(actualMessageId, url, "POST", data)

            val result = webClient
                .post()
                .uri(url)
                .header("X-Request-ID", actualMessageId)
                .header("X-Request-Body-Captured", objectMapper.writeValueAsString(data))
                .bodyValue(data)
                .retrieve()
                .bodyToMono(responseType)
                .awaitSingle()

            // Log successful request and response
            logger.debug("WebClient call succeeded, logging success for messageId: {}", actualMessageId)
            logSuccessfulResponse(savedRequest.id!!, 200, result)
            updateRequestStatus(savedRequest.id, "SUCCESS", null)
            return result

        } catch (exception: Exception) {
            logger.error(
                "Error during POST request to {} with messageId {}: {}",
                url, actualMessageId, exception.message, exception
            )

            // Log failed request and response (if available)
            logger.debug("WebClient call failed, logging failure for messageId: {}", actualMessageId)
            val savedRequest = findOrCreateFailedRequest(actualMessageId, url, "POST", data, exception)
            logFailedResponse(savedRequest.id!!, exception)

            // Re-throw the original exception to preserve error classification
            throw exception
        }
    }


    /**
     * Generates message ID for request correlation.
     */
    private fun generateMessageId(): String {
        return "REQ-${UUID.randomUUID().toString().take(32)}"
    }

    /**
     * Logs request attempt to database (before making HTTP call).
     */
    private suspend fun logRequestAttempt(
        messageId: String?,
        url: String,
        httpMethod: String,
        requestData: Any?
    ): ApiRequest {
        return try {
            val apiRequest = createApiRequest(messageId, url, httpMethod, requestData, "PENDING", null)
            val savedRequest = apiRequestRepository.save(apiRequest)
            logger.debug("Successfully logged request attempt to database: messageId={}", messageId)
            savedRequest
        } catch (e: Exception) {
            logger.warn("Failed to log request attempt to database: messageId={}, error={}", messageId, e.message)
            // Return a dummy request with generated ID for error handling
            createApiRequest(messageId, url, httpMethod, requestData, "PENDING", null).copy(id = -1L)
        }
    }

    /**
     * Finds existing request or creates failed request entry.
     */
    private suspend fun findOrCreateFailedRequest(
        messageId: String?,
        url: String,
        httpMethod: String,
        requestData: Any?,
        exception: Throwable
    ): ApiRequest {
        return try {
            // Try to find existing request first
            val existingRequest = messageId?.let { apiRequestRepository.findByMessageId(it) }
            if (existingRequest != null) {
                return existingRequest
            }

            // Create new failed request if not found
            val classification = httpErrorClassifier.classifyError(exception)
            val errorMessage = httpErrorClassifier.formatErrorMessage(classification, exception)
            val apiRequest = createApiRequest(messageId, url, httpMethod, requestData, "FAILED", errorMessage)
            apiRequestRepository.save(apiRequest)
        } catch (e: Exception) {
            logger.warn("Failed to find/create failed request: messageId={}, error={}", messageId, e.message)
            // Return dummy request for error handling
            createApiRequest(messageId, url, httpMethod, requestData, "FAILED", null).copy(id = -1L)
        }
    }

    /**
     * Logs successful HTTP response to database.
     */
    private suspend fun logSuccessfulResponse(apiRequestId: Long, statusCode: Int, responseBody: Any?) {
        try {
            val apiResponse = ApiResponse(
                apiRequestId = apiRequestId,
                responseStatusCode = statusCode,
                responseHeaders = createResponseHeaders(),
                responseBody = responseBody?.let { objectMapper.valueToTree<JsonNode>(it) },
                responseTimestamp = LocalDateTime.now()
            )
            apiResponseRepository.save(apiResponse)
            logger.debug("Successfully logged response to database: apiRequestId={}", apiRequestId)
        } catch (e: Exception) {
            logger.warn(
                "Failed to log successful response to database: apiRequestId={}, error={}",
                apiRequestId,
                e.message
            )
        }
    }

    /**
     * Logs failed HTTP response to database.
     */
    private suspend fun logFailedResponse(apiRequestId: Long, exception: Throwable) {
        try {
            val statusCode = when (exception) {
                is WebClientResponseException -> exception.statusCode.value()
                else -> 0 // Network errors don't have HTTP status codes
            }

            // Create exception details for response body
            val exceptionDetails = createExceptionResponseBody(exception)

            val apiResponse = ApiResponse(
                apiRequestId = apiRequestId,
                responseStatusCode = statusCode,
                responseHeaders = createErrorResponseHeaders(exception),
                responseBody = exceptionDetails,
                responseTimestamp = LocalDateTime.now()
            )
            apiResponseRepository.save(apiResponse)
            logger.debug("Successfully logged error response to database: apiRequestId={}", apiRequestId)
        } catch (e: Exception) {
            logger.warn("Failed to log error response to database: apiRequestId={}, error={}", apiRequestId, e.message)
        }
    }

    /**
     * Updates request status in database.
     */
    private suspend fun updateRequestStatus(apiRequestId: Long, status: String, errorMessage: String?) {
        try {
            val existingRequest = apiRequestRepository.findById(apiRequestId)
            existingRequest?.let { request ->
                val updatedRequest = request.copy(
                    requestStatus = status,
                    errorMessage = errorMessage ?: request.errorMessage
                )
                apiRequestRepository.save(updatedRequest)
                logger.debug("Updated request status: apiRequestId={}, status={}", apiRequestId, status)
            }
        } catch (e: Exception) {
            logger.warn("Failed to update request status: apiRequestId={}, error={}", apiRequestId, e.message)
        }
    }

    /**
     * Creates ApiRequest domain object for database logging.
     */
    private fun createApiRequest(
        messageId: String?,
        url: String,
        httpMethod: String,
        requestData: Any?,
        status: String,
        errorMessage: String?
    ): ApiRequest {
        val requestHeaders = createRequestHeaders(messageId, requestData)
        val requestBody = requestData?.let { objectMapper.valueToTree<JsonNode>(it) }

        return ApiRequest(
            messageId = messageId ?: generateMessageId(),
            url = url,
            httpMethod = httpMethod,
            requestHeaders = requestHeaders,
            requestBody = requestBody,
            requestTimestamp = LocalDateTime.now(),
            errorMessage = errorMessage,
            requestStatus = status,
        )
    }

    /**
     * Creates request headers JsonNode for logging.
     */
    private fun createRequestHeaders(messageId: String?, requestData: Any?): JsonNode {
        val headers = mutableMapOf<String, String>()
        messageId?.let { headers["X-Request-ID"] = it }
        requestData?.let {
            headers["X-Request-Body-Captured"] = objectMapper.writeValueAsString(it)
        }
        return objectMapper.valueToTree(headers)
    }

    /**
     * Creates response headers JsonNode for successful responses.
     */
    private fun createResponseHeaders(): JsonNode {
        val headers = mutableMapOf<String, String>()
        headers["Content-Type"] = "application/json"
        headers["Response-Logged-At"] = LocalDateTime.now().toString()
        return objectMapper.valueToTree(headers)
    }

    /**
     * Creates response headers JsonNode for error responses.
     */
    private fun createErrorResponseHeaders(exception: Throwable): JsonNode {
        val headers = mutableMapOf<String, String>()
        headers["Error-Type"] = exception::class.simpleName ?: "Unknown"
        headers["Error-Message"] = exception.message ?: "No message"
        headers["Response-Logged-At"] = LocalDateTime.now().toString()

        if (exception is WebClientResponseException) {
            exception.headers.forEach { (key, values) ->
                headers[key] = values.joinToString(", ")
            }
        }

        return objectMapper.valueToTree(headers)
    }

    /**
     * Creates exception details JsonNode for response body.
     */
    private fun createExceptionResponseBody(exception: Throwable): JsonNode {
        val exceptionData = mutableMapOf<String, Any?>()

        // Basic exception information
        exceptionData["errorType"] = exception::class.simpleName ?: "Unknown"
        exceptionData["errorMessage"] = exception.message ?: "No message"
        exceptionData["timestamp"] = LocalDateTime.now().toString()

        // Add HTTP-specific details if available
        if (exception is WebClientResponseException) {
            exceptionData["httpStatusCode"] = exception.statusCode.value()
            exceptionData["httpStatusText"] = exception.statusText
            exceptionData["responseBody"] = exception.responseBodyAsString
            exceptionData["requestMethod"] = exception.request?.method?.name()
            exceptionData["requestUrl"] = exception.request?.uri?.toString()
        }

        // Add error classification
        val classification = httpErrorClassifier.classifyError(exception)
        exceptionData["isRetryable"] = classification.isRetryable
        exceptionData["errorClassification"] = classification.errorType
        exceptionData["classificationDescription"] = classification.description

        // Add stack trace (limited to first 10 lines for readability)
        val stackTrace = exception.stackTrace.take(10).map { it.toString() }
        exceptionData["stackTrace"] = stackTrace

        // Add cause information if available
        exception.cause?.let { cause ->
            exceptionData["causeType"] = cause::class.simpleName
            exceptionData["causeMessage"] = cause.message
        }

        return objectMapper.valueToTree(exceptionData)
    }
}

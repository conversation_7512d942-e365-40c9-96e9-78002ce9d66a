package com.papertrl.visa.infrastructure.outgoing.visa.mapper

import com.papertrl.visa.domain.model.entity.GetPaymentControlsRequest
import com.papertrl.visa.domain.service.TenantBuyerLookupService
import com.papertrl.visa.infrastructure.outgoing.visa.config.properties.HttpClientProperties
import com.papertrl.visa.infrastructure.outgoing.visa.dto.GetPaymentControlsRequestDto
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Mapper for converting between GetPaymentControls domain model and Visa API DTOs.
 * Handles JSON parsing and data transformation for Visa API integration.
 */
@Component
class GetPaymentControlsMapper(
    private val httpClientProperties: HttpClientProperties,
    private val tenantBuyerLookupService: TenantBuyerLookupService
) {

    private val logger = LoggerFactory.getLogger(GetPaymentControlsMapper::class.java)

    /**
     * Convert domain model to Visa API request DTO.
     * Uses tenant-specific buyer ID lookup for dynamic buyer ID assignment.
     */
    suspend fun toRequestDto(domainRequest: GetPaymentControlsRequest): GetPaymentControlsRequestDto {
        logger.debug("Converting domain request to DTO for messageId: ${domainRequest.messageId}, tenantId: ${domainRequest.tenantId}")

        val buyerId = tenantBuyerLookupService.getBuyerIdForTenant(domainRequest.tenantId)
        logger.debug("Retrieved buyer ID: $buyerId for tenant: ${domainRequest.tenantId}")

        return GetPaymentControlsRequestDto(
            clientId = httpClientProperties.clientId,
            buyerId = buyerId,
            messageId = domainRequest.messageId,
            accountNumber = domainRequest.accountNumber
        )
    }
}

package com.papertrl.visa.infrastructure.outgoing.dbmanager.adapter

import com.papertrl.visa.domain.model.entity.ManagePaymentControlsRequest
import com.papertrl.visa.domain.model.enums.VisaRequestStatus
import com.papertrl.visa.domain.ports.outgoing.dbmanager.ManagePaymentControlsRepository
import com.papertrl.visa.domain.service.TenantBuyerLookupService
import com.papertrl.visa.infrastructure.outgoing.dbmanager.entity.ManagePaymentControlsRequestEntity
import com.papertrl.visa.infrastructure.outgoing.dbmanager.repository.ManagePaymentControlsRequestJpaRepository
import com.papertrl.visa.infrastructure.outgoing.visa.config.properties.HttpClientProperties
import org.springframework.stereotype.Component
import java.time.LocalDateTime

/**
 * Adapter implementation for ManagePaymentControlsRepository.
 * Bridges domain model with JPA infrastructure.
 */
@Component
class ManagePaymentControlsRepositoryAdapter(
    private val jpaRepository: ManagePaymentControlsRequestJpaRepository,
    private val httpClientProperties: HttpClientProperties,
    private val tenantBuyerLookupService: TenantBuyerLookupService
) : ManagePaymentControlsRepository {

    override suspend fun save(request: ManagePaymentControlsRequest): ManagePaymentControlsRequest {
        val entity = request.toEntity(httpClientProperties, tenantBuyerLookupService)
        val savedEntity = jpaRepository.save(entity)
        return savedEntity.toDomain()
    }

    override suspend fun findByMessageId(messageId: String): ManagePaymentControlsRequest? {
        return jpaRepository.findByMessageId(messageId)?.toDomain()
    }

    override suspend fun findByIdempotencyKeyAndStatus(
        idempotencyKey: String,
        status: VisaRequestStatus
    ): ManagePaymentControlsRequest? {
        return jpaRepository.findByIdempotencyKeyAndStatus(idempotencyKey, status)?.toDomain()
    }

    override suspend fun findById(id: Long): ManagePaymentControlsRequest? {
        return jpaRepository.findById(id).orElse(null)?.toDomain()
    }

    override suspend fun updateStatus(
        messageId: String,
        status: VisaRequestStatus,
        failureReason: String?
    ): ManagePaymentControlsRequest? {
        val entity = jpaRepository.findByMessageId(messageId)
        return entity?.let {
            val updatedEntity = it.copy(
                status = status,
                failureReason = failureReason,
                processedAt = if (status == VisaRequestStatus.SUCCESS) LocalDateTime.now() else it.processedAt,
                updatedDate = LocalDateTime.now()
            )
            jpaRepository.save(updatedEntity).toDomain()
        }
    }

    override suspend fun findAll(): List<ManagePaymentControlsRequest> {
        return jpaRepository.findAll().map { it.toDomain() }
    }

    override suspend fun deleteAll() {
        jpaRepository.deleteAll()
    }
}

/**
 * Extension function to convert domain model to JPA entity.
 */
suspend fun ManagePaymentControlsRequest.toEntity(
    httpClientProperties: HttpClientProperties,
    tenantBuyerLookupService: TenantBuyerLookupService
): ManagePaymentControlsRequestEntity {
    val buyerId = tenantBuyerLookupService.getBuyerIdForTenant(this.tenantId)
    return ManagePaymentControlsRequestEntity(
        id = this.id,
        messageId = this.messageId,
        idempotencyKey = this.idempotencyKey,
        tenantId = this.tenantId,
        clientId = httpClientProperties.clientId,
        buyerId = buyerId,
        accountNumber = this.accountNumber,
        expireOn = this.expireOn,
        status = this.status,
        failureReason = this.failureReason,
        processedAt = this.processedAt,
        createdBy = this.createdBy,
        createdDate = this.createdDate,
        updatedBy = this.updatedBy,
        updatedDate = this.updatedDate
    )
}

package com.papertrl.visa.infrastructure.outgoing.visa.evaluator

import com.papertrl.visa.infrastructure.outgoing.visa.config.properties.HttpClientProperties
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Evaluator for Visa API status codes specific to ProcessPayments endpoint.
 * Provides endpoint-specific validation and error handling logic.
 *
 * Note: Visa API returns HTTP 200 for both success and error responses,
 * using statusCode field for business-level error detection.
 */
@Component
class VisaStatusCodeEvaluator(
    private val httpClientProperties: HttpClientProperties
) {

    private val logger = LoggerFactory.getLogger(VisaStatusCodeEvaluator::class.java)

    /**
     * Evaluate if ProcessPayments response indicates success.
     * Uses configurable success status codes from properties.
     */
    fun isProcessPaymentsSuccess(statusCode: String): Boolean {
        val processPaymentsEndpoint = httpClientProperties.findEndpointByName("process-payments")
            ?: throw IllegalStateException("ProcessPayments endpoint not configured")
        val successCodes = processPaymentsEndpoint.successStatusCode.split(",").map { it.trim() }
        val isSuccess = successCodes.contains(statusCode)
        logger.debug(
            "ProcessPayments status code evaluation: {} -> {}",
            statusCode,
            if (isSuccess) "SUCCESS" else "FAILURE"
        )
        return isSuccess
    }

    /**
     * Evaluate if ProcessPayments response indicates a retryable error.
     * Simplified logic: all non-success responses are treated as non-retryable failures.
     */
    fun isProcessPaymentsRetryable(statusCode: String): Boolean {
        logger.debug("ProcessPayments retry evaluation: {} -> NON_RETRYABLE (simplified logic)", statusCode)
        return false // All non-success responses are treated as permanent failures
    }

    /**
     * Get human-readable error message for ProcessPayments status code.
     * Uses configurable success status codes from endpoint configuration.
     */
    fun getProcessPaymentsErrorMessage(statusCode: String): String {
        return if (isProcessPaymentsSuccess(statusCode)) {
            "Success"
        } else {
            "ProcessPayments failed with status code: $statusCode"
        }
    }

    /**
     * Evaluate if ManagePaymentControls response indicates success.
     * Uses configurable success status codes from properties.
     */
    fun isManagePaymentControlsSuccess(statusCode: String): Boolean {
        val managePaymentControlsEndpoint = httpClientProperties.findEndpointByName("manage-payment-controls")
            ?: throw IllegalStateException("ManagePaymentControls endpoint not configured")
        val successCodes = managePaymentControlsEndpoint.successStatusCode.split(",").map { it.trim() }
        val isSuccess = successCodes.contains(statusCode)
        logger.debug(
            "ManagePaymentControls status code evaluation: {} -> {}",
            statusCode,
            if (isSuccess) "SUCCESS" else "FAILURE"
        )
        return isSuccess
    }

    /**
     * Evaluate if ManagePaymentControls response indicates a retryable error.
     * Simplified logic: all non-success responses are treated as non-retryable failures.
     */
    fun isManagePaymentControlsRetryable(statusCode: String): Boolean {
        logger.debug("ManagePaymentControls retry evaluation: {} -> NON_RETRYABLE (simplified logic)", statusCode)
        return false // All non-success responses are treated as permanent failures
    }

    /**
     * Get human-readable error message for ManagePaymentControls status code.
     * Uses configurable success status codes from endpoint configuration.
     */
    fun getManagePaymentControlsErrorMessage(statusCode: String): String {
        return if (isManagePaymentControlsSuccess(statusCode)) {
            "Success"
        } else {
            "ManagePaymentControls failed with status code: $statusCode"
        }
    }

    /**
     * Evaluate if GetPaymentControls response indicates success.
     * Uses configurable success status codes from properties.
     */
    fun isGetPaymentControlsSuccess(statusCode: String): Boolean {
        val getPaymentControlsEndpoint = httpClientProperties.findEndpointByName("get-payment-controls")
            ?: throw IllegalStateException("GetPaymentControls endpoint not configured")
        val successCodes = getPaymentControlsEndpoint.successStatusCode.split(",").map { it.trim() }
        val isSuccess = successCodes.contains(statusCode)
        logger.debug(
            "GetPaymentControls status code evaluation: {} -> {}",
            statusCode,
            if (isSuccess) "SUCCESS" else "FAILURE"
        )
        return isSuccess
    }

    /**
     * Evaluate if GetPaymentControls response indicates a retryable error.
     * Simplified logic: all non-success responses are treated as non-retryable failures.
     */
    fun isGetPaymentControlsRetryable(statusCode: String): Boolean {
        logger.debug("GetPaymentControls retry evaluation: {} -> NON_RETRYABLE (simplified logic)", statusCode)
        return false // All non-success responses are treated as permanent failures
    }

    /**
     * Get human-readable error message for GetPaymentControls status code.
     * Uses configurable success status codes from endpoint configuration.
     */
    fun getGetPaymentControlsErrorMessage(statusCode: String): String {
        return if (isGetPaymentControlsSuccess(statusCode)) {
            "Success"
        } else {
            "GetPaymentControls failed with status code: $statusCode"
        }
    }

}

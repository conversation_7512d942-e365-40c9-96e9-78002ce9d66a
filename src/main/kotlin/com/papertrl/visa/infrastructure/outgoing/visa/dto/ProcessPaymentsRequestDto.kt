package com.papertrl.visa.infrastructure.outgoing.visa.dto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * DTO for Visa ProcessPayments API request.
 * Maps domain model to Visa API expected nested format.
 * Based on Visa ProcessPayments API specification with payment wrapper object.
 */
data class ProcessPaymentsRequestDto(
    @JsonProperty("actionType")
    val actionType: Int,

    @JsonProperty("clientId")
    val clientId: String,

    @JsonProperty("buyerId")
    val buyerId: String,

    @JsonProperty("messageId")
    val messageId: String,


    @JsonProperty("payment")
    val payment: PaymentDto
)



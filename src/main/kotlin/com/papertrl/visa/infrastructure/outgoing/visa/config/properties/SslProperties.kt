package com.papertrl.visa.infrastructure.outgoing.visa.config.properties

import org.springframework.boot.context.properties.ConfigurationProperties

/**
 * SSL configuration properties bound from application configuration.
 */
@ConfigurationProperties(prefix = "visa.ssl")
data class SslProperties(
    val enabled: Boolean = false,
    val keystore: KeystoreProperties = KeystoreProperties(),
    val key: KeyProperties = KeyProperties(),
    val protocol: String = "TLSv1.2",
    val enabledProtocols: String = "TLSv1.2",
    val useSameKeystoreForTrust: Boolean = true,
    val verifyHostname: Boolean = true
)

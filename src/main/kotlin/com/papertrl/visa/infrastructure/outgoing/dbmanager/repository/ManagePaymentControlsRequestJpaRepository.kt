package com.papertrl.visa.infrastructure.outgoing.dbmanager.repository

import com.papertrl.visa.domain.model.enums.VisaRequestStatus
import com.papertrl.visa.infrastructure.outgoing.dbmanager.entity.ManagePaymentControlsRequestEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

/**
 * JPA repository for ManagePaymentControlsRequestEntity.
 * Provides database access operations for payment control management requests.
 */
@Repository
interface ManagePaymentControlsRequestJpaRepository : JpaRepository<ManagePaymentControlsRequestEntity, Long> {

    /**
     * Find request by message ID.
     */
    fun findByMessageId(messageId: String): ManagePaymentControlsRequestEntity?

    /**
     * Find request by idempotency key and status.
     */
    fun findByIdempotencyKeyAndStatus(
        idempotencyKey: String,
        status: VisaRequestStatus
    ): ManagePaymentControlsRequestEntity?
}

package com.papertrl.visa.infrastructure.outgoing.visa.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal

/**
 * DTO for invoice information in ProcessPayments request.
 * Contains invoice details for payment processing.
 * Updated to match VISA API specification with invoiceDate field.
 */
data class InvoiceDto(
    @JsonProperty("invoiceNumber")
    val invoiceNumber: String,

    @JsonProperty("invoiceAmount")
    val invoiceAmount: BigDecimal,

    @JsonProperty("invoiceDate")
    val invoiceDate: String
)

package com.papertrl.visa.infrastructure.outgoing.visa.dto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * DTO for Visa GetPaymentControls API request.
 * Maps domain model to Visa API expected format.
 * Based on Visa GetPaymentControls API specification.
 */
data class GetPaymentControlsRequestDto(
    @JsonProperty("clientId")
    val clientId: String,

    @JsonProperty("buyerId")
    val buyerId: String,

    @JsonProperty("messageId")
    val messageId: String,

    @JsonProperty("accountNumber")
    val accountNumber: String
)

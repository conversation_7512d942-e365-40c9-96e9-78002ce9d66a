package com.papertrl.visa.infrastructure.outgoing.dbmanager.entity

import jakarta.persistence.*

/**
 * JPA Entity for tenant buyer configuration.
 * Stores tenant-specific buyer ID mappings for Visa API integration.
 * Simplified for admin-managed data.
 */
@Entity
@Table(name = "tenant_buyer_config")
data class TenantBuyerConfigEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    @Column(name = "tenant_id", length = 100, nullable = false, unique = true)
    val tenantId: String,

    @Column(name = "buyer_id", length = 100, nullable = false)
    val buyerId: String,

    @Column(name = "is_active", nullable = false)
    val isActive: Boolean = true
)

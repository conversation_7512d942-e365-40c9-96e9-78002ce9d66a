package com.papertrl.visa.infrastructure.outgoing.visa.dto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * DTO for Visa ProcessPayments API response.
 * Handles both success and error responses for the process-payment API.
 */
data class ProcessPaymentsResponseDto(
    @JsonProperty("messageId")
    val messageId: String,

    @JsonProperty("statusCode")
    val statusCode: String,

    @JsonProperty("statusDesc")
    val statusDesc: String,

    @JsonProperty("accountNumber")
    val accountNumber: String? = null,

    @JsonProperty("expirationDate")
    val expirationDate: String? = null
)



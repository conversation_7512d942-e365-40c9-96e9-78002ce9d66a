package com.papertrl.visa.infrastructure.outgoing.dbmanager.repository

import com.papertrl.visa.domain.model.enums.VisaRequestStatus
import com.papertrl.visa.infrastructure.outgoing.dbmanager.entity.ProcessPaymentsRequestEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

/**
 * JPA Repository interface for ProcessPaymentsRequestEntity.
 * Provides database operations for ProcessPayments request entities.
 */
@Repository
interface ProcessPaymentsRequestJpaRepository : JpaRepository<ProcessPaymentsRequestEntity, Long> {

    /**
     * Find a ProcessPayments request by message ID.
     */
    fun findByMessageId(messageId: String): ProcessPaymentsRequestEntity?

    /**
     * Find a ProcessPayments request by idempotency key and status.
     */
    fun findByIdempotencyKeyAndStatus(idempotencyKey: String, status: VisaRequestStatus): ProcessPaymentsRequestEntity?
}

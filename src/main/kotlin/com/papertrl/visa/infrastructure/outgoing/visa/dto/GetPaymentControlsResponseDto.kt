package com.papertrl.visa.infrastructure.outgoing.visa.dto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * DTO for Visa GetPaymentControls API response.
 * Maps the actual Visa API response structure to domain model.
 * Based on actual Visa GetPaymentControls API response format.
 */
data class GetPaymentControlsResponseDto(
    @JsonProperty("messageId")
    val messageId: String,

    @JsonProperty("statusCode")
    val statusCode: String,

    @JsonProperty("statusDesc")
    val statusDesc: String? = null,

    @JsonProperty("rules")
    val rules: List<GetPaymentControlsRuleDto>? = null,

    @JsonProperty("mcgRuleAction")
    val mcgRuleAction: String? = null
)

/**
 * DTO for rule object in GetPaymentControls response.
 * Represents individual payment control rules with their overrides.
 */
data class GetPaymentControlsRuleDto(
    @JsonProperty("ruleCode")
    val ruleCode: String,

    @JsonProperty("overrides")
    val overrides: List<GetPaymentControlsOverrideDto>
)

/**
 * DTO for override object in GetPaymentControls response.
 * Represents individual override values within a rule.
 */
data class GetPaymentControlsOverrideDto(
    @JsonProperty("sequence")
    val sequence: String,

    @JsonProperty("overrideCode")
    val overrideCode: String,

    @JsonProperty("overrideValue")
    val overrideValue: String
)

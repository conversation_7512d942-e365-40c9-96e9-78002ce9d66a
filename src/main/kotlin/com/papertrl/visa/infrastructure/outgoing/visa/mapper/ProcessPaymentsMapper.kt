package com.papertrl.visa.infrastructure.outgoing.visa.mapper

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.papertrl.visa.domain.model.entity.ProcessPaymentsRequest
import com.papertrl.visa.domain.service.TenantBuyerLookupService
import com.papertrl.visa.infrastructure.outgoing.visa.config.properties.HttpClientProperties
import com.papertrl.visa.infrastructure.outgoing.visa.dto.*
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Mapper for converting between ProcessPayments domain model and Visa API DTOs.
 * Handles JSON parsing and data transformation for Visa API integration.
 */
@Component
class ProcessPaymentsMapper(
    private val objectMapper: ObjectMapper,
    private val httpClientProperties: HttpClientProperties,
    private val tenantBuyerLookupService: TenantBuyerLookupService
) {

    private val logger = LoggerFactory.getLogger(ProcessPaymentsMapper::class.java)

    /**
     * Convert domain model to Visa API request DTO with nested payment structure.
     * Uses tenant-specific buyer ID lookup for dynamic buyer ID assignment.
     */
    suspend fun toRequestDto(domainRequest: ProcessPaymentsRequest): ProcessPaymentsRequestDto {
        logger.debug("Converting domain request to DTO for messageId: ${domainRequest.messageId}, tenantId: ${domainRequest.tenantId}")

        val buyerId = tenantBuyerLookupService.getBuyerIdForTenant(domainRequest.tenantId)
        logger.debug("Retrieved buyer ID: $buyerId for tenant: ${domainRequest.tenantId}")

        return ProcessPaymentsRequestDto(
            actionType = domainRequest.actionType,
            messageId = domainRequest.messageId,
            clientId = httpClientProperties.clientId,
            buyerId = buyerId,
            payment = PaymentDto(
                paymentExpiryDate = domainRequest.paymentExpiryDate,
                accountType = domainRequest.accountType,
                currencyCode = domainRequest.currencyCode,
                paymentGrossAmount = domainRequest.paymentGrossAmount,
                paymentType = domainRequest.paymentType,
                invoices = parseInvoices(domainRequest.invoicesData),
                supplier = mapSupplier(domainRequest)
            )
        )
    }


    /**
     * Map domain supplier data to DTO with flattened address structure.
     */
    private fun mapSupplier(domainRequest: ProcessPaymentsRequest): SupplierDto {
        return SupplierDto(
            supplierName = domainRequest.supplierName,
            supplierID = domainRequest.supplierID,
            supplierCity = domainRequest.supplierCity,
            supplierAddressLine1 = domainRequest.supplierAddressLine1,
            supplierAddressLine2 = domainRequest.supplierAddressLine2,
            supplierState = domainRequest.supplierState,
            supplierCountryCode = domainRequest.supplierCountryCode,
            supplierPostalCode = domainRequest.supplierPostalCode,
            primaryEmailAddress = domainRequest.primaryEmailAddress,
            alternateEmailAddresses = parseAlternateEmailsToDto(domainRequest.alternateEmailAddressesData),
            emailNotes = domainRequest.emailNotes
        )
    }

    /**
     * Parse invoices JsonNode data to DTO list with invoiceDate field.
     */
    private fun parseInvoices(invoicesData: JsonNode): List<InvoiceDto> {
        return try {
            val typeRef = object : TypeReference<List<Map<String, Any>>>() {}
            val invoicesList = objectMapper.treeToValue(invoicesData, typeRef)

            invoicesList.map { invoiceMap ->
                InvoiceDto(
                    invoiceNumber = invoiceMap["invoiceNumber"]?.toString() ?: "",
                    invoiceAmount = (invoiceMap["invoiceAmount"] as? Number)?.let {
                        java.math.BigDecimal(it.toString())
                    } ?: java.math.BigDecimal.ZERO,
                    invoiceDate = invoiceMap["invoiceDate"]?.toString() ?: ""
                )
            }
        } catch (e: Exception) {
            logger.error("Failed to parse invoices data: $invoicesData", e)
            emptyList()
        }
    }

    /**
     * Parse alternate email addresses JsonNode data to DTO format.
     * The stored data format is: [{"alternateEmailAddress":"<EMAIL>"}, {"alternateEmailAddress":"<EMAIL>"}]
     */
    private fun parseAlternateEmailsToDto(alternateEmailsData: JsonNode?): List<AlternateEmailDto>? {
        if (alternateEmailsData == null || alternateEmailsData.isNull) return null

        return try {
            // The data is already stored in the correct format as List<AlternateEmailDto>
            val typeRef = object : TypeReference<List<AlternateEmailDto>>() {}
            objectMapper.treeToValue(alternateEmailsData, typeRef)
        } catch (e: Exception) {
            logger.error("Failed to parse alternate emails data: $alternateEmailsData", e)
            null
        }
    }


}

package com.papertrl.visa.infrastructure.outgoing.dbmanager.adapter

import com.papertrl.visa.domain.model.entity.ApiRequest
import com.papertrl.visa.domain.ports.outgoing.dbmanager.ApiRequestRepository
import com.papertrl.visa.infrastructure.outgoing.dbmanager.entity.ApiRequestEntity
import com.papertrl.visa.infrastructure.outgoing.dbmanager.repository.ApiRequestJpaRepository
import org.springframework.stereotype.Component

/**
 * Adapter implementation for ApiRequestRepository.
 * Bridges domain model with JPA infrastructure.
 */
@Component
class ApiRequestRepositoryAdapter(
    private val jpaRepository: ApiRequestJpaRepository
) : ApiRequestRepository {

    override suspend fun save(apiRequest: ApiRequest): ApiRequest {
        val entity = apiRequest.toEntity()
        val savedEntity = jpaRepository.save(entity)
        return savedEntity.toDomain()
    }

    override suspend fun findByMessageId(messageId: String): ApiRequest? {
        return jpaRepository.findByMessageId(messageId)?.toDomain()
    }

    override suspend fun findById(id: Long): ApiRequest? {
        return jpaRepository.findById(id).orElse(null)?.toDomain()
    }

    /**
     * Convert domain model to entity.
     */
    private fun ApiRequest.toEntity(): ApiRequestEntity {
        return ApiRequestEntity(
            id = this.id,
            messageId = this.messageId,
            url = this.url,
            httpMethod = this.httpMethod,
            requestHeaders = this.requestHeaders,
            requestBody = this.requestBody,
            requestTimestamp = this.requestTimestamp,
            errorMessage = this.errorMessage,
            requestStatus = this.requestStatus,
            retryCount = this.retryCount
        )
    }

    /**
     * Convert entity to domain model.
     */
    private fun ApiRequestEntity.toDomain(): ApiRequest {
        return ApiRequest(
            id = this.id,
            messageId = this.messageId,
            url = this.url,
            httpMethod = this.httpMethod,
            requestHeaders = this.requestHeaders,
            requestBody = this.requestBody,
            requestTimestamp = this.requestTimestamp,
            errorMessage = this.errorMessage,
            requestStatus = this.requestStatus,
            retryCount = this.retryCount
        )
    }
}

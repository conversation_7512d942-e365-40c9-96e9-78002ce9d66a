package com.papertrl.visa.infrastructure.outgoing.visa.config.properties

/**
 * Configuration for a single API endpoint.
 * Contains all necessary information to configure and validate an endpoint.
 */
data class EndpointConfig(
    val name: String = "",
    val path: String = "",
    val successStatusCode: String = "",
    val method: String = "POST",
    val timeout: Long? = null,
    val description: String = ""
)

package com.papertrl.visa.infrastructure.outgoing.visa.config.properties

import org.springframework.boot.context.properties.ConfigurationProperties

/**
 * HTTP client configuration properties for Visa API communication.
 */
@ConfigurationProperties(prefix = "visa.api")
data class HttpClientProperties(
    val baseUrl: String = "https://cert.api.visa.com",
    val timeout: Long = 30000,
    val connectionTimeout: Long = 10000,
    val readTimeout: Long = 30000,
    val writeTimeout: Long = 30000,
    val userId: String = "",
    val password: String = "",
    val clientId: String = "",
    val buyerId: String = "",
    val retry: RetryProperties = RetryProperties(),
    val endpointConfigs: List<EndpointConfig> = emptyList()
) {

    /**
     * Find endpoint configuration by name.
     * @param name The endpoint name
     * @return The endpoint configuration or null if not found
     */
    fun findEndpointByName(name: String): EndpointConfig? {
        return endpointConfigs.find { it.name.equals(name, ignoreCase = true) }
    }

}



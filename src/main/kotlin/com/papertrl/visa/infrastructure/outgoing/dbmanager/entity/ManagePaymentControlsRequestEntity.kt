package com.papertrl.visa.infrastructure.outgoing.dbmanager.entity

import com.papertrl.visa.domain.model.entity.ManagePaymentControlsRequest
import com.papertrl.visa.domain.model.enums.VisaRequestStatus
import jakarta.persistence.*
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * JPA entity for ManagePaymentControls requests extending domain model.
 * Infrastructure entity that extends domain model rather than duplicating structure.
 * Maps to existing manage_payment_controls_requests table.
 */
@Entity
@Table(name = "manage_payment_controls_requests")
data class ManagePaymentControlsRequestEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    @Column(name = "message_id", length = 100, nullable = false, unique = true)
    val messageId: String,

    @Column(name = "idempotency_key", length = 100, nullable = false)
    val idempotencyKey: String,

    @Column(name = "tenant_id", length = 100, nullable = false)
    val tenantId: String,

    @Column(name = "client_id", length = 100, nullable = false)
    val clientId: String,

    @Column(name = "buyer_id", length = 100, nullable = false)
    val buyerId: String,

    @Column(name = "account_number", length = 50, nullable = false)
    val accountNumber: String,

    @Column(name = "expire_on", nullable = false)
    val expireOn: LocalDate,

    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 20, nullable = false)
    val status: VisaRequestStatus = VisaRequestStatus.PENDING,

    @Column(name = "failure_reason", length = 1000)
    val failureReason: String? = null,

    @Column(name = "processed_at")
    val processedAt: LocalDateTime? = null,

    @Column(name = "created_by", length = 100, nullable = false)
    val createdBy: String = "SYSTEM",

    @Column(name = "created_date", nullable = false)
    val createdDate: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_by", length = 100, nullable = false)
    val updatedBy: String = "SYSTEM",

    @Column(name = "updated_date", nullable = false)
    val updatedDate: LocalDateTime = LocalDateTime.now()
) {
    /**
     * Convert JPA entity to domain model.
     */
    fun toDomain(): ManagePaymentControlsRequest {
        return ManagePaymentControlsRequest(
            id = this.id,
            messageId = this.messageId,
            idempotencyKey = this.idempotencyKey,
            tenantId = this.tenantId,
            accountNumber = this.accountNumber,
            expireOn = this.expireOn,
            status = this.status,
            failureReason = this.failureReason,
            processedAt = this.processedAt,
            createdBy = this.createdBy,
            createdDate = this.createdDate,
            updatedBy = this.updatedBy,
            updatedDate = this.updatedDate
        )
    }
}

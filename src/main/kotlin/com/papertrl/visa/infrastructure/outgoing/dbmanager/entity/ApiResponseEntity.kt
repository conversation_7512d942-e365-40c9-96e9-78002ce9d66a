package com.papertrl.visa.infrastructure.outgoing.dbmanager.entity

import com.fasterxml.jackson.databind.JsonNode
import jakarta.persistence.*
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.type.SqlTypes
import java.time.LocalDateTime

/**
 * JPA Entity for logging incoming API responses.
 * Stores comprehensive response data linked to requests via foreign key.
 * Supports webhook responses (apiRequestId can be null).
 */
@Entity
@Table(name = "api_response")
data class ApiResponseEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    @Column(name = "api_request_id")
    val apiRequestId: Long? = null, // Can be null for webhook responses

    @Column(name = "response_status_code", nullable = false)
    val responseStatusCode: Int,

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "response_headers")
    val responseHeaders: JsonNode? = null,

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "response_body")
    val responseBody: JsonNode? = null,

    @Column(name = "response_timestamp", nullable = false)
    val responseTimestamp: LocalDateTime = LocalDateTime.now()
)

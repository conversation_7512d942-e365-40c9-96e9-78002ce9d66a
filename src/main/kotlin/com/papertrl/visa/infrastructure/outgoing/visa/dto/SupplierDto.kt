package com.papertrl.visa.infrastructure.outgoing.visa.dto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * DTO for supplier information in ProcessPayments request.
 * Contains supplier details with flattened address fields for VISA API.
 * Updated to match VISA API specification with flat address structure.
 */
data class SupplierDto(
    @JsonProperty("supplierName")
    val supplierName: String,

    @JsonProperty("supplierID")
    val supplierID: Long,

    @JsonProperty("supplierCity")
    val supplierCity: String,

    @JsonProperty("supplierAddressLine1")
    val supplierAddressLine1: String,

    @JsonProperty("supplierAddressLine2")
    val supplierAddressLine2: String? = null,

    @JsonProperty("supplierState")
    val supplierState: String,

    @JsonProperty("supplierCountryCode")
    val supplierCountryCode: String,

    @JsonProperty("supplierPostalCode")
    val supplierPostalCode: String,

    @JsonProperty("primaryEmailAddress")
    val primaryEmailAddress: String,

    @JsonProperty("alternateEmailAddresses")
    val alternateEmailAddresses: List<AlternateEmailDto>? = null,

    @JsonProperty("emailNotes")
    val emailNotes: String? = null
)

package com.papertrl.visa.infrastructure.outgoing.dbmanager.adapter

import com.papertrl.visa.domain.model.entity.ApiResponse
import com.papertrl.visa.domain.ports.outgoing.dbmanager.ApiResponseRepository
import com.papertrl.visa.infrastructure.outgoing.dbmanager.entity.ApiResponseEntity
import com.papertrl.visa.infrastructure.outgoing.dbmanager.repository.ApiResponseJpaRepository
import org.springframework.stereotype.Component

/**
 * Adapter implementation for ApiResponseRepository.
 * Bridges domain model with JPA infrastructure.
 */
@Component
class ApiResponseRepositoryAdapter(
    private val jpaRepository: ApiResponseJpaRepository
) : ApiResponseRepository {

    override suspend fun save(apiResponse: ApiResponse): ApiResponse {
        val entity = apiResponse.toEntity()
        val savedEntity = jpaRepository.save(entity)
        return savedEntity.toDomain()
    }

    override suspend fun findById(id: Long): ApiResponse? {
        return jpaRepository.findById(id).orElse(null)?.toDomain()
    }

    override suspend fun findByApiRequestId(apiRequestId: Long): List<ApiResponse> {
        return jpaRepository.findByApiRequestId(apiRequestId).map { it.toDomain() }
    }


    /**
     * Convert domain model to entity.
     */
    private fun ApiResponse.toEntity(): ApiResponseEntity {
        return ApiResponseEntity(
            id = this.id,
            apiRequestId = this.apiRequestId,
            responseStatusCode = this.responseStatusCode,
            responseHeaders = this.responseHeaders,
            responseBody = this.responseBody,
            responseTimestamp = this.responseTimestamp
        )
    }

    /**
     * Convert entity to domain model.
     */
    private fun ApiResponseEntity.toDomain(): ApiResponse {
        return ApiResponse(
            id = this.id,
            apiRequestId = this.apiRequestId,
            responseStatusCode = this.responseStatusCode,
            responseHeaders = this.responseHeaders,
            responseBody = this.responseBody,
            responseTimestamp = this.responseTimestamp
        )
    }
}

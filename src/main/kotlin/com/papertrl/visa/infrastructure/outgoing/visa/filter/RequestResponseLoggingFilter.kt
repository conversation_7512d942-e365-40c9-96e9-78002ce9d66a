package com.papertrl.visa.infrastructure.outgoing.visa.filter

import com.papertrl.visa.domain.model.dto.RequestData
import com.papertrl.visa.domain.model.dto.ResponseData
import com.papertrl.visa.domain.ports.incoming.dbmanager.ApiLoggingUseCase
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import org.springframework.core.io.buffer.DataBufferUtils
import org.springframework.web.reactive.function.client.ClientRequest
import org.springframework.web.reactive.function.client.ClientResponse
import org.springframework.web.reactive.function.client.ExchangeFilterFunction
import reactor.core.publisher.Mono
import reactor.util.context.Context
import java.nio.charset.StandardCharsets
import java.time.LocalDateTime

/**
 * WebClient filter for comprehensive request/response logging.
 * Always captures full request and response body content and integrates with domain layer through ApiLoggingUseCase.
 */
class RequestResponseLoggingFilter(
    private val apiLoggingUseCase: ApiLoggingUseCase,
    private val loggingEnabled: Boolean = true
) {

    private val logger = LoggerFactory.getLogger(RequestResponseLoggingFilter::class.java)

    /**
     * Create the exchange filter function for request/response logging.
     * Uses proper WebClient filter pattern with reactor context for message ID correlation.
     */
    fun filter(): ExchangeFilterFunction {
        return ExchangeFilterFunction { request, next ->
            if (!loggingEnabled) {
                return@ExchangeFilterFunction next.exchange(request)
            }

            // Extract message ID from request header (always present)
            val messageId = request.headers().getFirst("X-Request-ID")!!
            val requestTimestamp = LocalDateTime.now()

            // Console logging
            logger.info("Outgoing Request: ${request.method()} ${request.url()}")

            // Create request data
            val requestData = createRequestData(request, messageId, requestTimestamp)

            // Log request to domain layer
            try {
                runBlocking {
                    apiLoggingUseCase.logRequest(requestData)
                }
                logger.debug("Request logged to domain layer for messageId: $messageId")
            } catch (e: Exception) {
                logger.error("Failed to log request to domain layer for messageId: $messageId", e)
            }

            // Remove X-Request-Body-Captured header before sending request to external API
            val cleanedRequest = ClientRequest.from(request)
                .headers { headers ->
                    headers.remove("X-Request-Body-Captured")
                }
                .build()

            // Execute the cleaned request and handle response with proper body capture
            next.exchange(cleanedRequest)
                .flatMap { response ->
                    logger.info("Incoming Response: ${response.statusCode()} for messageId: $messageId")
                    captureResponseWithBody(response, messageId)
                }
                .contextWrite(Context.of("messageId", messageId))
        }
    }

    /**
     * Create RequestData from ClientRequest.
     */
    private fun createRequestData(
        request: ClientRequest,
        messageId: String,
        timestamp: LocalDateTime
    ): RequestData {
        val headers =
            request.headers().toSingleValueMap().filterKeys { !it.equals("X-Request-Body-Captured", ignoreCase = true) }

        // Get request body from header (set by ReactiveHttpClient)
        val requestBody = if (hasRequestBody(request)) {
            request.headers().getFirst("X-Request-Body-Captured") ?: "[Request body - capture at service level]"
        } else {
            null
        }

        return RequestData(
            messageId = messageId,
            method = request.method().toString(),
            url = request.url().toString(),
            headers = headers,
            body = requestBody,
            timestamp = timestamp
        )
    }


    /**
     * Create ResponseData from ClientResponse.
     */
    private fun createResponseData(
        response: ClientResponse,
        messageId: String,
        body: String?
    ): ResponseData {
        val headers = response.headers().asHttpHeaders().toSingleValueMap()

        return ResponseData(
            messageId = messageId,
            statusCode = response.statusCode().value(),
            headers = headers,
            body = body,
            timestamp = LocalDateTime.now()
        )
    }


    /**
     * Capture response with body content using the correct WebClient pattern.
     * This approach uses DataBufferUtils to properly handle the response stream without consuming it.
     */
    private fun captureResponseWithBody(
        response: ClientResponse,
        messageId: String
    ): Mono<ClientResponse> {
        return response.mutate()
            .body { originalBodyFlux ->
                // Use DataBufferUtils to join and cache the body content
                DataBufferUtils.join(originalBodyFlux)
                    .doOnNext { dataBuffer ->
                        // Read the content for logging
                        val responseBodyContent = try {
                            val bytes = ByteArray(dataBuffer.readableByteCount())
                            dataBuffer.read(bytes)
                            // Reset the buffer position for downstream consumption
                            dataBuffer.readPosition(0)
                            String(bytes, StandardCharsets.UTF_8)
                        } catch (e: Exception) {
                            logger.warn("Failed to read response body for logging: ${e.message}")
                            "[Body read failed: ${e.message}]"
                        }

                        // Log response to domain layer
                        val responseData = createResponseData(response, messageId, responseBodyContent)
                        try {
                            runBlocking {
                                apiLoggingUseCase.updateWithResponse(messageId, responseData)
                            }
                            logger.debug("Response logged to domain layer for messageId: $messageId")
                        } catch (e: Exception) {
                            logger.error("Failed to log response to domain layer for messageId: $messageId", e)
                        }
                    }
                    .flux()
                    .onErrorResume { error ->
                        logger.error("Failed to capture response body for messageId: $messageId", error)

                        // Log response without body on error
                        val responseData =
                            createResponseData(response, messageId, "[Body capture failed: ${error.message}]")
                        try {
                            runBlocking {
                                apiLoggingUseCase.updateWithResponse(messageId, responseData)
                            }
                        } catch (e: Exception) {
                            logger.error("Failed to log error response to domain layer for messageId: $messageId", e)
                        }

                        // Return original stream on error
                        originalBodyFlux
                    }
            }
            .build()
            .let { Mono.just(it) }
    }


    /**
     * Check if request has a body (POST, PUT, PATCH methods typically have bodies).
     */
    private fun hasRequestBody(request: ClientRequest): Boolean {
        val method = request.method()
        return method.toString() == "POST" || method.toString() == "PUT" || method.toString() == "PATCH"
    }

}

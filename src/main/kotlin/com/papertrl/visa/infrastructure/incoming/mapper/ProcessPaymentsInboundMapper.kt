package com.papertrl.visa.infrastructure.incoming.mapper

import com.fasterxml.jackson.databind.ObjectMapper
import com.papertrl.visa.domain.model.entity.ProcessPaymentsRequest
import com.papertrl.visa.infrastructure.incoming.dto.generated.PaymentTransactionDto
import com.papertrl.visa.infrastructure.incoming.dto.generated.PaymentTransactionSummaryDto
import org.springframework.stereotype.Component
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter

/**
 * Mapper for converting standardized inbound DTOs to domain models.
 * Handles the transformation from PaymentTransactionDto to domain entities.
 * Converts standardized DTO structure to flat domain model with JSON strings.
 */
@Component
class ProcessPaymentsInboundMapper(
    private val objectMapper: ObjectMapper
) {

    /**
     * Convert standardized PaymentTransactionDto to domain model.
     * Maps PaymentTransactionDto fields to ProcessPaymentsRequest domain model.
     * Converts standardized DTO structure to flat domain model with JSON strings.
     *
     * @param dto The PaymentTransactionDto containing payment details
     * @param idempotencyKey The idempotency key from the DTO
     * @param tenantId The tenant identifier from the DTO
     * @return The domain model with all required fields
     */
    fun toDomain(dto: PaymentTransactionDto, idempotencyKey: String, tenantId: String): ProcessPaymentsRequest {
        val recipientDetail = dto.recipientDetail
        val transactionSummaryList = dto.transactionSummaryList ?: emptyList()

        // Convert transaction summary list to invoices JsonNode
        val invoicesJson = convertTransactionSummaryToInvoices(transactionSummaryList)

        // Convert alternate email addresses to JsonNode (if available)
        val alternateEmailsJson = null // can't clarify - no alternate emails in new structure

        return ProcessPaymentsRequest(
            idempotencyKey = idempotencyKey,
            tenantId = tenantId,
            actionType = 1,
            paymentExpiryDate = formatDateToYYYYMMDD(dto.expireOn),
            accountType = recipientDetail?.accountType?.toIntOrNull() ?: 2, // can't clarify - converting string to int
            currencyCode = "USD", // can't clarify - no currency in new structure
            paymentGrossAmount = dto.txnAmount ?: java.math.BigDecimal.ZERO,
            paymentType = "STP", // can't clarify - no payment type in new structure
            supplierName = recipientDetail?.companyName
                ?: recipientDetail?.let { "${it.recipientFirstName ?: ""} ${it.recipientLastName ?: ""}" }?.trim()
                ?: "",
            supplierID = recipientDetail?.vendorId?.toLong() ?: 0L,
            supplierAddressLine2 = recipientDetail?.addressLine2,
            supplierAddressLine1 = recipientDetail?.addressLine1 ?: "",
            supplierCity = recipientDetail?.city ?: "",
            supplierState = recipientDetail?.addressState ?: "",
            supplierCountryCode = recipientDetail?.country ?: "",
            supplierPostalCode = recipientDetail?.zipcode ?: "",
            primaryEmailAddress = recipientDetail?.email ?: "",
            emailNotes = dto.comment,
            invoicesData = invoicesJson,
            alternateEmailAddressesData = alternateEmailsJson
        )
    }

    /**
     * Convert PaymentTransactionSummaryDto list to invoices JsonNode.
     * Maps transaction summary fields to invoice structure.
     * Ensures invoiceAmount is properly formatted as double value.
     */
    private fun convertTransactionSummaryToInvoices(summaryList: List<PaymentTransactionSummaryDto>): com.fasterxml.jackson.databind.JsonNode {
        val invoices = summaryList.map { summary ->
            mapOf(
                "invoiceNumber" to (summary.billNumber ?: ""),
                "invoiceAmount" to (summary.billAmount?.toDouble() ?: 0.0),
                "invoiceDate" to (summary.paymentDateStr ?: "")
            )
        }
        return objectMapper.valueToTree(invoices)
    }

    /**
     * Format OffsetDateTime to YYYY-MM-DD string format as required by PaymentExpirationDate validation.
     * Returns only date part without time (e.g., "2026-06-30").
     * Returns empty string if date is null.
     */
    private fun formatDateToYYYYMMDD(dateTime: OffsetDateTime?): String {
        return if (dateTime != null) {
            dateTime.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
        } else {
            ""
        }
    }

}

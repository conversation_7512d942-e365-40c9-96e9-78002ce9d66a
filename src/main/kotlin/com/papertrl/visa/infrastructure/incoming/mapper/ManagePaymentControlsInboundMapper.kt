package com.papertrl.visa.infrastructure.incoming.mapper

import com.papertrl.visa.domain.model.entity.ManagePaymentControlsRequest
import com.papertrl.visa.domain.service.AccountLookupService
import com.papertrl.visa.infrastructure.incoming.dto.generated.PaymentTransactionDto
import org.springframework.stereotype.Component

/**
 * Mapper for converting inbound DTOs to domain models.
 * Handles the transformation from PaymentTransactionDto to ManagePaymentControlsRequest domain entity.
 * Uses AccountLookupService to retrieve account number from process_payments_request table.
 */
@Component
class ManagePaymentControlsInboundMapper(
    private val accountLookupService: AccountLookupService
) {


    /**
     * Convert PaymentTransactionDto to domain model for ManagePaymentControls.
     * Uses tpId to lookup accountNumber from process_payments_request table.
     * Follows the expected flow specification for data retrieval.
     *
     * @param dto The PaymentTransactionDto containing payment control request data
     * @param idempotencyKey The idempotency key from the DTO
     * @param tenantId The tenant identifier from the DTO
     * @return The domain model with all required fields
     */
    suspend fun toDomain(dto: PaymentTransactionDto, idempotencyKey: String, tenantId: String): ManagePaymentControlsRequest {
        // Get accountNumber from process_payments_request table using tpId as messageId
        val tpId = dto.tpId ?: throw IllegalArgumentException("tpId is required for account lookup")
        val accountNumber = accountLookupService.getAccountNumberByTpId(tpId)

        // Extract expireOn from dto.expireOn and convert to LocalDate
        val expireOn = dto.expireOn?.toLocalDate()
            ?: throw IllegalArgumentException("expireOn is required in the request")

        return ManagePaymentControlsRequest(
            idempotencyKey = idempotencyKey,
            tenantId = tenantId,
            accountNumber = accountNumber,
            expireOn = expireOn
        )
    }

}

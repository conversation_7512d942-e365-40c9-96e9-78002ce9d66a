package com.papertrl.visa.infrastructure.incoming.adapter

import com.papertrl.visa.domain.model.enums.VisaRequestStatus
import com.papertrl.visa.domain.ports.incoming.visa.ManagePaymentControlsUseCase
import com.papertrl.visa.domain.ports.incoming.visa.ProcessPaymentsUseCase
import com.papertrl.visa.infrastructure.incoming.api.VisaPaymentsApi
import com.papertrl.visa.infrastructure.incoming.dto.generated.*
import com.papertrl.visa.infrastructure.incoming.mapper.ManagePaymentControlsInboundMapper
import com.papertrl.visa.infrastructure.incoming.mapper.PapertrlResponseMapper
import com.papertrl.visa.infrastructure.incoming.mapper.ProcessPaymentsInboundMapper
import jakarta.validation.ConstraintViolationException
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

/**
 * Inbound adapter for ProcessPayments requests implementing OpenAPI generated interface.
 * Handles input validation at the adapter level before passing data to domain layer.
 * Ensures only pre-validated, clean data enters the domain logic.
 * Returns responses in Papertrl format with messageId mapped to tpTxnId.
 * Provides consistent error handling with proper PapertrlTransactionResponseDto structure
 * for both successful and failed payment processing requests.
 */
@RestController
@RequestMapping("/api/v1")
class ProcessPaymentsInboundAdapter(
    private val processPaymentsUseCase: ProcessPaymentsUseCase,
    private val inboundMapper: ProcessPaymentsInboundMapper,
    private val papertrlResponseMapper: PapertrlResponseMapper,
    private val managePaymentControlsUseCase: ManagePaymentControlsUseCase,
    private val managePaymentControlsInboundMapper: ManagePaymentControlsInboundMapper
) : VisaPaymentsApi {

    private val logger = LoggerFactory.getLogger(ProcessPaymentsInboundAdapter::class.java)

    /**
     * Process a payment request with input validation.
     * Validation occurs at this adapter level using @Valid annotation.
     * Only validated data is passed to the domain layer.
     * Updated to accept standardized PaymentTransactionDto and return PapertrlTransactionResponseDto.
     * Maps messageId to tpTxnId in the response as required by Papertrl service.
     * Implements the OpenAPI generated interface method.
     *
     * Error Handling:
     * - Returns consistent PapertrlTransactionResponseDto structure for both success and error cases
     * - Maps domain messageId to tpTxnId in error responses (or generates fallback ID)
     * - Sets appropriate HTTP status codes (400 for client errors, 500 for server errors)
     * - Identifies client input data issues automatically
     * - Provides meaningful failure reasons from exceptions
     */
    override fun processPayment(paymentTransactionDto: PaymentTransactionDto): ResponseEntity<PapertrlTransactionResponseDto> {
        logger.info("Received ProcessPayments request for validation and processing with idempotencyKey: ${paymentTransactionDto.idempotencyKey}, tenantId: ${paymentTransactionDto.tenantId}")

        var domainMessageId: String? = null

        return try {
            // Convert standardized DTO to domain model with idempotency key and tenant ID
            val domainRequest = inboundMapper.toDomain(
                paymentTransactionDto,
                paymentTransactionDto.idempotencyKey,
                paymentTransactionDto.tenantId
            )
            domainMessageId = domainRequest.messageId
            logger.debug("Converted standardized DTO to domain model for messageId: ${domainRequest.messageId}, idempotencyKey: ${domainRequest.idempotencyKey}")

            // Process through domain layer (no validation needed - already done)
            val processedRequest = runBlocking { processPaymentsUseCase.processPayment(domainRequest) }
            logger.info("Successfully processed payment for messageId: ${processedRequest.messageId}, status: ${processedRequest.status}")

            // Convert to Papertrl response DTO using mapper
            val responseDto = papertrlResponseMapper.toPapertrlResponse(processedRequest)

            ResponseEntity.ok(responseDto)

        } catch (e: Exception) {
            logger.error("Failed to process payment request", e)

            // Create error response with proper PapertrlTransactionResponseDto structure
            val errorResponse = createErrorResponse(e, domainMessageId)
            val httpStatus = determineHttpStatus(e)

            ResponseEntity.status(httpStatus).body(errorResponse)
        }
    }

    /**
     * Create error response with PapertrlTransactionResponseDto structure.
     * Ensures consistent response format for both successful and failed requests.
     */
    private fun createErrorResponse(exception: Exception, messageId: String?): PapertrlTransactionResponseDto {
        val tpTxnId = messageId ?: generateFallbackTxnId()
        val isClientInputError = isClientInputError(exception)
        val failReason = getFailureReason(exception)

        logger.debug("Creating error response for tpTxnId: $tpTxnId, clientInputError: $isClientInputError, reason: $failReason")

        return PapertrlTransactionResponseDto(
            id = null, // No ID available for failed requests
            tpTxnId = tpTxnId,
            txnType = null, // Transaction type not available in error cases
            canceled = true, // All failed transactions are considered canceled
            tpStatus = VisaRequestStatus.FAILED.name,
            failReason = failReason,
            httpStatusCode = determineHttpStatus(exception).value(),
            clientInputDataIssue = isClientInputError,
            cardDetails = null // No card details available in error cases
        )
    }

    /**
     * Determine appropriate HTTP status code based on exception type.
     */
    private fun determineHttpStatus(exception: Exception): HttpStatus {
        return when {
            isClientInputError(exception) -> HttpStatus.BAD_REQUEST // 400
            else -> HttpStatus.INTERNAL_SERVER_ERROR // 500
        }
    }

    /**
     * Determine if the exception represents a client input error.
     */
    private fun isClientInputError(exception: Exception): Boolean {
        return when (exception) {
            is ConstraintViolationException -> true
            is IllegalArgumentException -> true
            is IllegalStateException -> exception.message?.let { msg ->
                msg.lowercase().contains("validation") ||
                        msg.lowercase().contains("invalid") ||
                        msg.lowercase().contains("required") ||
                        msg.lowercase().contains("missing")
            } ?: false

            else -> {
                val message = exception.message?.lowercase() ?: ""
                message.contains("validation") ||
                        message.contains("invalid") ||
                        message.contains("required") ||
                        message.contains("missing") ||
                        message.contains("bad request") ||
                        message.contains("malformed")
            }
        }
    }

    /**
     * Manage payment controls with input validation.
     * Validation occurs at this adapter level using @Valid annotation.
     * Only validated data is passed to the domain layer.
     * Updated to accept standardized PaymentTransactionDto for consistency.
     * Implements the OpenAPI generated interface method.
     */
    override fun managePaymentControls(paymentTransactionDto: PaymentTransactionDto): ResponseEntity<PapertrlTransactionResponseDto> {
        logger.info("Received ManagePaymentControls request for validation and processing with idempotencyKey: ${paymentTransactionDto.idempotencyKey}, tenantId: ${paymentTransactionDto.tenantId}")

        return try {
            // Convert standardized DTO to domain model with idempotency key and tenant ID
            val domainRequest = runBlocking {
                managePaymentControlsInboundMapper.toDomain(
                    paymentTransactionDto,
                    paymentTransactionDto.idempotencyKey,
                    paymentTransactionDto.tenantId
                )
            }
            logger.debug("Converted standardized DTO to domain model for messageId: ${domainRequest.messageId}, idempotencyKey: ${domainRequest.idempotencyKey}")

            // Process through domain layer (no validation needed - already done)
            val processedRequest = runBlocking { managePaymentControlsUseCase.managePaymentControls(domainRequest) }
            logger.info("Successfully processed payment controls for messageId: ${processedRequest.messageId}, status: ${processedRequest.status}")

            // Convert domain result to PapertrlTransactionResponseDto
            val responseDto = PapertrlTransactionResponseDto(
                id = null, // No specific ID for manage payment controls
                tpTxnId = processedRequest.messageId,
                txnType = null, // No specific transaction type for manage payment controls
                canceled = false,
                tpStatus = processedRequest.status.name,
                failReason = null,
                httpStatusCode = 200,
                clientInputDataIssue = false,
                cardDetails = null // No card details for manage payment controls
            )

            ResponseEntity.ok(responseDto)

        } catch (e: Exception) {
            logger.error(
                "ManagePaymentControls request failed for idempotencyKey: ${paymentTransactionDto.idempotencyKey}",
                e
            )

            val errorResponse = PapertrlTransactionResponseDto(
                id = null,
                tpTxnId = paymentTransactionDto.idempotencyKey ?: "ERROR",
                txnType = null,
                canceled = true,
                tpStatus = "FAILED",
                failReason = e.message ?: "Unknown error occurred",
                httpStatusCode = HttpStatus.INTERNAL_SERVER_ERROR.value(),
                clientInputDataIssue = isClientInputError(e),
                cardDetails = null
            )

            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse)
        }
    }

    /**
     * Extract meaningful failure reason from exception.
     */
    private fun getFailureReason(exception: Exception): String {
        return when {
            exception.message.isNullOrBlank() -> "Payment processing failed due to an unexpected error"
            else -> exception.message!!
        }
    }

    /**
     * Generate fallback transaction ID when domain messageId is not available.
     */
    private fun generateFallbackTxnId(): String {
        return "ERR-${UUID.randomUUID().toString().take(33)}"
    }





}

package com.papertrl.visa.infrastructure.incoming.mapper

import com.papertrl.visa.domain.model.entity.ProcessPaymentsRequest
import com.papertrl.visa.domain.model.enums.VisaRequestStatus
import com.papertrl.visa.infrastructure.incoming.dto.generated.PapertrlCardDetailsDto
import com.papertrl.visa.infrastructure.incoming.dto.generated.PapertrlTransactionResponseDto
import org.springframework.stereotype.Component

/**
 * Mapper for converting domain models to Papertrl response DTOs.
 * Maps internal payment processing results to Papertrl expected format.
 */
@Component
class PapertrlResponseMapper {

    /**
     * Convert ProcessPaymentsRequest domain model to PapertrlTransactionResponseDto.
     * Maps messageId to tpTxnId as required by Papertrl service.
     */
    fun toPapertrlResponse(processedRequest: ProcessPaymentsRequest): PapertrlTransactionResponseDto {
        return PapertrlTransactionResponseDto(
            id = processedRequest.id?.toInt(),
            tpTxnId = processedRequest.messageId, // messageId maps to tpTxnId
            txnType = mapToTxnType(processedRequest.actionType),
            canceled = processedRequest.status == VisaRequestStatus.FAILED,
            tpStatus = processedRequest.status.name,
            failReason = processedRequest.failureReason,
            httpStatusCode = mapStatusToHttpCode(processedRequest.status),
            clientInputDataIssue = isClientInputDataIssue(processedRequest.failureReason),
            cardDetails = createCardDetails()
        )
    }

    /**
     * Map domain action type to Papertrl transaction type.
     */
    private fun mapToTxnType(actionType: Int): Int {
        // Map action type to transaction type based on business logic
        // For now, return the same value, but this can be customized
        return actionType
    }



    /**
     * Map domain status to HTTP status code.
     */
    private fun mapStatusToHttpCode(status: VisaRequestStatus): Int {
        return when (status) {
            VisaRequestStatus.PENDING -> 202 // Accepted
            VisaRequestStatus.PROCESSING -> 202 // Accepted
            VisaRequestStatus.SUCCESS -> 200 // OK
            VisaRequestStatus.RETRY -> 202 // Accepted
            VisaRequestStatus.FAILED -> 400 // Bad Request
        }
    }

    /**
     * Determine if the failure is due to client input data issue.
     */
    private fun isClientInputDataIssue(failureReason: String?): Boolean {
        if (failureReason == null) return false

        val clientErrorKeywords = listOf(
            "validation", "invalid", "missing", "required",
            "format", "malformed", "bad request", "input"
        )

        return clientErrorKeywords.any { keyword ->
            failureReason.lowercase().contains(keyword)
        }
    }

    /**
     * Create card details from processed request.
     * For now, returns null as card details are not available in current domain model.
     * This can be enhanced when card information is added to the domain model.
     */
    private fun createCardDetails(): PapertrlCardDetailsDto? {
        // Card details are not currently available in the domain model
        // This would need to be populated from Visa API response when available
        // When card details become available, they can be mapped here
        return null
    }
}

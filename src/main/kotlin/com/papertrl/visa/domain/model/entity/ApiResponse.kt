package com.papertrl.visa.domain.model.entity

import com.fasterxml.jackson.databind.JsonNode
import java.time.LocalDateTime

/**
 * Pure domain model for API responses.
 * Contains business logic without any infrastructure concerns.
 * Framework-agnostic - no JPA annotations or external dependencies.
 */
data class ApiResponse(
    val id: Long? = null,
    val apiRequestId: Long? = null, // Can be null for webhook responses
    val responseStatusCode: Int,
    val responseHeaders: JsonNode? = null,
    val responseBody: JsonNode? = null,
    val responseTimestamp: LocalDateTime = LocalDateTime.now()
)

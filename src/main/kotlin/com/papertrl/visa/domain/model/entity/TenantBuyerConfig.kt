package com.papertrl.visa.domain.model.entity

/**
 * Pure domain model for tenant-specific buyer ID configuration.
 * Contains business logic for tenant-buyer mapping without infrastructure concerns.
 * Framework-agnostic - no JPA annotations or external dependencies.
 * Simplified for admin-managed data.
 */
data class TenantBuyerConfig(
    val id: Long? = null,
    val tenantId: String,
    val buyerId: String,
    val isActive: Boolean = true
) {

    /**
     * Validates that the tenant configuration is active and properly configured.
     * @return true if the configuration is valid for use
     */
    fun isValidForUse(): Boolean {
        return isActive && tenantId.isNotBlank() && buyerId.isNotBlank()
    }
}

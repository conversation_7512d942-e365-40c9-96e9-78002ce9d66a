package com.papertrl.visa.domain.model.dto

import java.time.LocalDateTime

/**
 * Framework-agnostic data object for HTTP response information.
 * Used for logging and audit purposes in the domain layer.
 */
data class ResponseData(
    val messageId: String,
    val statusCode: Int,
    val headers: Map<String, String>,
    val body: String?,
    val timestamp: LocalDateTime = LocalDateTime.now()
)

package com.papertrl.visa.domain.model.enums

/**
 * Enum representing the processing status of Visa API requests.
 * Tracks the lifecycle of request processing through Visa API for both
 * ProcessPayments and ManagePaymentControls operations.
 */
enum class VisaRequestStatus {
    /**
     * Request has been received and saved but not yet processed.
     */
    PENDING,

    /**
     * Request is currently being processed by Visa API.
     */
    PROCESSING,

    /**
     * Request was processed successfully by Visa API.
     */
    SUCCESS,

    /**
     * Request failed but can be retried (temporary failure).
     */
    RETRY,

    /**
     * Request failed permanently and should not be retried.
     */
    FAILED
}

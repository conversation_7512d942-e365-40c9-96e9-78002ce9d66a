package com.papertrl.visa.domain.model.entity

import com.fasterxml.jackson.databind.JsonNode
import java.time.LocalDateTime

/**
 * Pure domain model for API requests.
 * Contains business logic without any infrastructure concerns.
 * Framework-agnostic - no JPA annotations or external dependencies.
 */
data class ApiRequest(
    val id: Long? = null,
    val messageId: String,
    val url: String,
    val httpMethod: String,
    val requestHeaders: JsonNode? = null,
    val requestBody: JsonNode? = null,
    val requestTimestamp: LocalDateTime = LocalDateTime.now(),
    val errorMessage: String? = null,
    val requestStatus: String = "SUCCESS",
    val retryCount: Int = 0
)

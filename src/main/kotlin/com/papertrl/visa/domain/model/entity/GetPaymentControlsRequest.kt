package com.papertrl.visa.domain.model.entity

import com.fasterxml.jackson.databind.JsonNode
import java.util.*

/**
 * Simple data transfer object for GetPaymentControls requests.
 * Used as an internal utility for retrieving payment control data from Visa API.
 * No persistence - this is a supporting service for other domain operations.
 */
data class GetPaymentControlsRequest(
    val messageId: String = "GPC-${UUID.randomUUID().toString().take(32)}",
    val tenantId: String,
    val accountNumber: String,
    val retrievedPaymentControlsData: JsonNode? = null // JSON representation of retrieved payment controls
) {

    /**
     * Create a copy with retrieved payment controls data.
     */
    fun withRetrievedData(retrievedPaymentControlsData: JsonNode?): GetPaymentControlsRequest {
        return copy(retrievedPaymentControlsData = retrievedPaymentControlsData)
    }
}

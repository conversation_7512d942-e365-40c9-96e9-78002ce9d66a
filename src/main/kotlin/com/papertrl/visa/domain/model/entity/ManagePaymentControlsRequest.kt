package com.papertrl.visa.domain.model.entity

import com.papertrl.visa.domain.model.enums.VisaRequestStatus
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

/**
 * Pure domain model for ManagePaymentControls requests.
 * Contains business logic without any infrastructure concerns.
 * Framework-agnostic - no JPA annotations or external dependencies.
 */
data class ManagePaymentControlsRequest(
    val id: Long? = null,
    val messageId: String = "MPC-${UUID.randomUUID().toString().take(32)}",
    val idempotencyKey: String,
    val tenantId: String,
    val accountNumber: String,
    val expireOn: LocalDate,
    val status: VisaRequestStatus = VisaRequestStatus.PENDING,
    val failureReason: String? = null,
    val processedAt: LocalDateTime? = null,
    val createdBy: String = "SYSTEM",
    val createdDate: LocalDateTime = LocalDateTime.now(),
    val updatedBy: String = "SYSTEM",
    val updatedDate: LocalDateTime = LocalDateTime.now()
) {

    /**
     * Create a copy with updated status.
     */
    fun withStatus(
        newStatus: VisaRequestStatus,
        failureReason: String? = null,
        processedAt: LocalDateTime? = null
    ): ManagePaymentControlsRequest {
        return copy(
            status = newStatus,
            failureReason = failureReason,
            processedAt = processedAt,
            updatedDate = LocalDateTime.now()
        )
    }
}

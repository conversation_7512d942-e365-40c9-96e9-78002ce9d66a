package com.papertrl.visa.domain.model.entity

import com.fasterxml.jackson.databind.JsonNode
import com.papertrl.visa.domain.model.enums.VisaRequestStatus
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.*

/**
 * Pure domain model for ProcessPayments requests.
 * Contains business logic without any infrastructure concerns.
 * Framework-agnostic - no JPA annotations or external dependencies.
 */
data class ProcessPaymentsRequest(
    val id: Long? = null,
    val messageId: String = "PP-${UUID.randomUUID().toString().take(33)}",
    val idempotencyKey: String,
    val tenantId: String,
    val actionType: Int,
    val paymentExpiryDate: String,
    val accountType: Int,
    val currencyCode: String,
    val paymentGrossAmount: BigDecimal,
    val paymentType: String,
    val supplierName: String,
    val supplierID: Long,
    val supplierAddressLine2: String? = null,
    val supplierAddressLine1: String,
    val supplierCity: String,
    val supplierState: String,
    val supplierCountryCode: String,
    val supplierPostalCode: String,
    val primaryEmailAddress: String,
    val emailNotes: String? = null,
    val invoicesData: JsonNode, // JSON representation of invoices
    val alternateEmailAddressesData: JsonNode? = null, // JSON representation
    val status: VisaRequestStatus = VisaRequestStatus.PENDING,
    val failureReason: String? = null,
    val processedAt: LocalDateTime? = null,
    val accountNumber: String? = null,
    val expirationDate: String? = null,
    val createdBy: String = "SYSTEM",
    val createdDate: LocalDateTime = LocalDateTime.now(),
    val updatedBy: String = "SYSTEM",
    val updatedDate: LocalDateTime = LocalDateTime.now()
) {

    /**
     * Create a copy with updated status.
     */
    fun withStatus(
        newStatus: VisaRequestStatus,
        failureReason: String? = null,
        processedAt: LocalDateTime? = null
    ): ProcessPaymentsRequest {
        return copy(
            status = newStatus,
            failureReason = failureReason,
            processedAt = processedAt,
            updatedDate = LocalDateTime.now()
        )
    }
}

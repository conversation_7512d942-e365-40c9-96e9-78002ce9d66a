package com.papertrl.visa.domain.model.dto

import java.time.LocalDateTime

/**
 * Framework-agnostic data object for HTTP request information.
 * Used for logging and audit purposes in the domain layer.
 */
data class RequestData(
    val messageId: String,
    val method: String,
    val url: String,
    val headers: Map<String, String>,
    val body: String?,
    val timestamp: LocalDateTime = LocalDateTime.now()
)

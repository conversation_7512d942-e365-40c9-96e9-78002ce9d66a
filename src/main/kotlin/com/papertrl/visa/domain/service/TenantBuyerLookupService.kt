package com.papertrl.visa.domain.service

import com.papertrl.visa.domain.ports.outgoing.dbmanager.TenantBuyerConfigRepository
import com.papertrl.visa.infrastructure.outgoing.visa.config.properties.HttpClientProperties
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * Domain service for tenant-specific buyer ID lookup.
 * Handles the business logic for retrieving tenant-specific buyer IDs
 * with fallback to default configuration for backward compatibility.
 * Simplified for admin-managed tenant configurations.
 */
@Service
class TenantBuyerLookupService(
    private val tenantBuyerConfigRepository: TenantBuyerConfigRepository,
    private val httpClientProperties: HttpClientProperties
) {

    private val logger = LoggerFactory.getLogger(TenantBuyerLookupService::class.java)

    /**
     * Retrieves the buyer ID for a specific tenant.
     * Falls back to default configuration if tenant-specific config is not found.
     *
     * @param tenantId The tenant identifier
     * @return The buyer ID for the tenant
     * @throws RuntimeException if no configuration is found
     */
    suspend fun getBuyerIdForTenant(tenantId: String): String {
        logger.debug("Looking up buyer ID for tenant: $tenantId")

        // First, try to find tenant-specific configuration
        val tenantConfig = tenantBuyerConfigRepository.findActiveByTenantId(tenantId)

        if (tenantConfig != null && tenantConfig.isValidForUse()) {
            logger.debug("Found tenant-specific buyer ID for tenant: $tenantId, buyerId: ${tenantConfig.buyerId}")
            return tenantConfig.buyerId
        }

        // Fallback to default configuration from properties
        if (httpClientProperties.buyerId.isNotBlank()) {
            logger.warn("No tenant-specific configuration found for tenant: $tenantId, using default buyer ID: ${httpClientProperties.buyerId}")
            return httpClientProperties.buyerId
        }

        // If no configuration is found at all, throw exception
        logger.error("No buyer ID configuration found for tenant: $tenantId")
        throw RuntimeException("No buyer ID configuration found for tenant: $tenantId")
    }
}

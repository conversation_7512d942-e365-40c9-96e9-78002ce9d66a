package com.papertrl.visa.domain.service

import com.papertrl.visa.domain.ports.outgoing.dbmanager.ProcessPaymentsRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * Service for looking up account information from process payments data.
 * Provides account number retrieval using tpId as the lookup key.
 */
@Service
class AccountLookupService(
    private val processPaymentsRepository: ProcessPaymentsRepository
) {
    
    private val logger = LoggerFactory.getLogger(AccountLookupService::class.java)

    /**
     * Retrieve account number from process_payments_request table using tpId as messageId.
     * 
     * @param tpId The third-party identifier used as messageId for lookup
     * @return The account number associated with the tpId
     * @throws IllegalArgumentException if no account is found for the given tpId
     */
    suspend fun getAccountNumberByTpId(tpId: String): String {
        logger.debug("Looking up account number for tpId: $tpId")
        
        val processPaymentRequest = processPaymentsRepository.findByMessageId(tpId)
        
        return processPaymentRequest?.accountNumber
            ?: throw IllegalArgumentException("No account found for tpId: $tpId")
            .also { logger.error("Account lookup failed for tpId: $tpId") }
    }
}

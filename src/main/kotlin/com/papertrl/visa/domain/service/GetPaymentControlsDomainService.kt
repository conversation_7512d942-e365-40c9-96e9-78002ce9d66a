package com.papertrl.visa.domain.service

import com.papertrl.visa.domain.model.entity.GetPaymentControlsRequest
import com.papertrl.visa.domain.ports.incoming.visa.GetPaymentControlsUseCase
import com.papertrl.visa.domain.ports.outgoing.visa.VisaApiClient
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * Internal utility service for GetPaymentControls operations.
 * Provides payment control retrieval functionality for other domain services.
 * No persistence - this is a supporting service that only makes Visa API calls.
 *
 * Architecture: Internal Service → Visa API Client
 * - Simplified service for internal use by other domain services
 * - Makes direct Visa API calls without persistence
 * - Returns retrieved payment control data
 */
@Service
class GetPaymentControlsDomainService(
    private val visaApiClient: VisaApiClient
) : GetPaymentControlsUseCase {

    private val logger = LoggerFactory.getLogger(GetPaymentControlsDomainService::class.java)

    /**
     * Retrieve payment controls from Visa API.
     * Simplified utility service that makes direct API calls without persistence.
     *
     * @param request The GetPaymentControls request with account information
     * @return The request with retrieved payment controls data
     * @throws RuntimeException if API call fails
     */
    override suspend fun getPaymentControls(request: GetPaymentControlsRequest): GetPaymentControlsRequest {
        logger.info("Retrieving payment controls from Visa API for messageId: ${request.messageId}, accountNumber: ${request.accountNumber}")

        return try {
            // Make direct Visa API call to retrieve payment controls
            val responseRequest = visaApiClient.sendGetPaymentControlsRequest(request)

            logger.info("Successfully retrieved payment controls for messageId: ${request.messageId}")
            return responseRequest

        } catch (e: Exception) {
            logger.error("GetPaymentControls API call failed for messageId: ${request.messageId}", e)
            throw RuntimeException("Failed to retrieve payment controls: ${e.message}", e)
        }
    }
}

package com.papertrl.visa.domain.service

import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * Service for processing expireOn values according to the expected flow specification.
 * Handles conditional date formatting based on override codes and business rules.
 */
@Service
class ExpireOnProcessor {

    companion object {
        private val MM_DD_YYYY_FORMATTER = DateTimeFormatter.ofPattern("MM/dd/yyyy")
        private val YYYY_MM_DD_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    }

    /**
     * Process expireOn value according to the expected flow specification.
     *
     * Business Rules:
     * - If overrideCode equals "endDate", set expireOn value to overrideValue field (format: MM/DD/YYYY without time)
     * - Otherwise, set expireOn value to paymentControlDetails.endDate (format: YYYY-MM-DD without time)
     *
     * @param expireOn The original expireOn date from the request
     * @param overrideCode The override code from payment control details (can be null)
     * @param overrideValue The override value from payment control details (can be null)
     * @return Formatted date string according to business rules
     */
    fun processExpireOnValue(
        expireOn: LocalDate
    ): String {
        return expireOn.format(MM_DD_YYYY_FORMATTER)
    }

    /**
     * Extract endDate value for paymentControlDetails.
     * Always returns YYYY-MM-DD format without time.
     */
    fun formatEndDateForPaymentControlDetails(expireOn: LocalDate): String {
        return expireOn.format(YYYY_MM_DD_FORMATTER)
    }
}

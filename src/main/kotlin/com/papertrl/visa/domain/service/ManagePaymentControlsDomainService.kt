package com.papertrl.visa.domain.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.papertrl.visa.domain.model.entity.GetPaymentControlsRequest
import com.papertrl.visa.domain.model.entity.ManagePaymentControlsRequest
import com.papertrl.visa.domain.model.enums.VisaRequestStatus
import com.papertrl.visa.domain.ports.incoming.visa.GetPaymentControlsUseCase
import com.papertrl.visa.domain.ports.incoming.visa.ManagePaymentControlsUseCase
import com.papertrl.visa.domain.ports.outgoing.dbmanager.ManagePaymentControlsRepository
import com.papertrl.visa.domain.ports.outgoing.visa.VisaApiClient
import com.papertrl.visa.infrastructure.outgoing.visa.dto.*
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * Primary service for ManagePaymentControls business logic and orchestration.
 * Implements ManagePaymentControlsUseCase interface following the expected flow specification.
 * Orchestrates the simplified payment control processing flow.
 *
 * Architecture: Inbound Port → Domain Service → Outbound Ports
 * - Handles account lookup, payment control retrieval, and override extraction
 * - Manages Visa API integration and database persistence
 * - Provides comprehensive error handling and logging
 */
@Service
class ManagePaymentControlsDomainService(
    private val visaApiClient: VisaApiClient,
    private val managePaymentControlsRepository: ManagePaymentControlsRepository,
    private val getPaymentControlsUseCase: GetPaymentControlsUseCase,
    private val paymentControlOverrideExtractor: PaymentControlOverrideExtractor,
    private val expireOnProcessor: ExpireOnProcessor,
    private val objectMapper: ObjectMapper
) : ManagePaymentControlsUseCase {

    private val logger = LoggerFactory.getLogger(ManagePaymentControlsDomainService::class.java)

    /**
     * Process payment controls following the expected flow specification.
     * Implements ManagePaymentControlsUseCase interface with simplified business logic.
     *
     * @param request The ManagePaymentControls domain request
     * @return The processed request with updated status
     * @throws RuntimeException if processing fails after all retries
     */
    override suspend fun managePaymentControls(request: ManagePaymentControlsRequest): ManagePaymentControlsRequest {
        // Step 0: Idempotency check - Query for successful requests with same idempotencyKey
        logger.debug("Checking idempotency for key: ${request.idempotencyKey}")
        val existingSuccessfulRequest = managePaymentControlsRepository.findByIdempotencyKeyAndStatus(
            request.idempotencyKey,
            VisaRequestStatus.SUCCESS
        )

        if (existingSuccessfulRequest != null) {
            logger.info("Idempotent request found for key: ${request.idempotencyKey}, returning existing result")
            return existingSuccessfulRequest
        }

        // Step 1: Save request to database
        logger.debug("ManagePaymentControls request received for processing, messageId: ${request.messageId}, idempotencyKey: ${request.idempotencyKey}")
        val savedRequest = managePaymentControlsRepository.save(request)
        logger.debug("ManagePaymentControls request saved with ID: ${savedRequest.id}, idempotencyKey: ${savedRequest.idempotencyKey}")

        // Step 2: Update status to processing
        val processingRequest = updateRequestStatus(savedRequest, VisaRequestStatus.PROCESSING)

        try {
            // Step 3: Retrieve existing payment controls using GetPaymentControls API
            logger.info("Retrieving existing payment controls for messageId: ${request.messageId}, accountNumber: ${processingRequest.accountNumber}")
            val getPaymentControlsRequest = GetPaymentControlsRequest(
                tenantId = processingRequest.tenantId,
                accountNumber = processingRequest.accountNumber
            )

            val retrievedControlsRequest = getPaymentControlsUseCase.getPaymentControls(getPaymentControlsRequest)
            val retrievedRules = extractRulesFromResponse(retrievedControlsRequest)
            logger.info("Successfully retrieved ${retrievedRules?.size ?: 0} payment control rules for messageId: ${request.messageId}")

            // Step 4: Extract specific overrides from retrieved payment controls
            val extractedOverrides = paymentControlOverrideExtractor.extractSpecificOverrides(
                retrievedRules,
                request.messageId
            )
            logger.info("Successfully extracted ${extractedOverrides.size} specific overrides for messageId: ${request.messageId}")

            // Step 5: Build payment control details with extracted overrides and processed endDate
            val paymentControlDetails = buildPaymentControlDetails(processingRequest, extractedOverrides)

            // Step 6: Send the request to ManagePaymentControls API
            logger.debug("Executing ManagePaymentControls request for message ID: ${request.messageId}")
            val responseRequest =
                visaApiClient.sendManagePaymentControlsRequest(processingRequest, paymentControlDetails)

            // Step 7: Evaluate Visa API response and set appropriate status
            val finalRequest = evaluateAndUpdateRequestStatus(responseRequest)

            logger.info("ManagePaymentControls completed for message ID: ${finalRequest.messageId}, final status: ${finalRequest.status}")
            return finalRequest

        } catch (e: Exception) {
            logger.error("ManagePaymentControls processing failed for message ID: ${request.messageId}", e)
            val failedRequest = updateRequestStatus(processingRequest, VisaRequestStatus.FAILED, e.message)
            throw RuntimeException("ManagePaymentControls processing failed: ${e.message}", e)
        }
    }

    /**
     * Build payment control details with extracted overrides and processed endDate.
     * Implements the expected flow logic for endDate processing and override value updates.
     */
    private fun buildPaymentControlDetails(
        request: ManagePaymentControlsRequest,
        extractedOverrides: List<OverrideDto>
    ): List<PaymentControlDetailDto> {
        // Process endDate using ExpireOnProcessor with proper business logic
        val endDateOverride = extractedOverrides.find { it.overrideCode == "endDate" }
        val processedEndDate = expireOnProcessor.processExpireOnValue(
            request.expireOn
        )

        // Format endDate for paymentControlDetails (always YYYY-MM-DD)
        val paymentControlEndDate = expireOnProcessor.formatEndDateForPaymentControlDetails(request.expireOn)

        // Update the endDate override value with the processed date
        // This implements the expected flow requirement for endDate transformation
        val updatedOverrides = extractedOverrides.map { override ->
            if (override.overrideCode == "endDate") {
                override.copy(overrideValue = processedEndDate)
            } else {
                override
            }
        }

        // Process startDate with YYYY/MM/DD formatting
        val startDateOverride = updatedOverrides.find { it.overrideCode == "startDate" }
        val formattedStartDate = if (startDateOverride != null) {
            // Format the extracted startDate value to YYYY/MM/DD
            formatDateToYyyyMmDd(startDateOverride.overrideValue)
        } else {
            // Use paymentControlEndDate as fallback, convert from YYYY-MM-DD to YYYY/MM/DD
            paymentControlEndDate.replace("-", "/")
        }

        // Build the payment control details structure
        return listOf(
            PaymentControlDetailDto(
                rulesSet = listOf(
                    RulesSetDto(
                        action = "R",
                        rules = listOf(
                            RuleDto(
                                ruleCode = "SPV",
                                overrides = updatedOverrides
                            )
                        )
                    )
                ),
                endDate = paymentControlEndDate,
                mcgRuleAction = "Block",
                timeZone = "UTC-0",
                startDate = formattedStartDate
            )
        )
    }

    /**
     * Evaluate Visa API response and set appropriate request status.
     * Since Visa API response fields are no longer stored, we simply mark as SUCCESS.
     */
    private suspend fun evaluateAndUpdateRequestStatus(request: ManagePaymentControlsRequest): ManagePaymentControlsRequest {
        logger.debug("Evaluating Visa API response for messageId: ${request.messageId}")

        // If we reach this point, the Visa API call was successful
        logger.info("Visa API success for messageId: ${request.messageId}")
        return updateRequestStatus(
            request = request,
            status = VisaRequestStatus.SUCCESS
        )
    }

    /**
     * Update request status and persist to database.
     */
    private suspend fun updateRequestStatus(
        request: ManagePaymentControlsRequest,
        status: VisaRequestStatus,
        failureReason: String? = null
    ): ManagePaymentControlsRequest {
        val updatedRequest = request.withStatus(
            newStatus = status,
            failureReason = failureReason,
            processedAt = if (status == VisaRequestStatus.SUCCESS) LocalDateTime.now() else null
        )

        return managePaymentControlsRepository.save(updatedRequest)
    }

    /**
     * Handle ManagePaymentControls errors with appropriate status updates.
     * Since retry logic is now handled at infrastructure level (VisaApiClientAdapter),
     * any error reaching this level indicates a permanent failure.
     */
    private suspend fun handleManagePaymentControlsError(
        request: ManagePaymentControlsRequest,
        error: Exception
    ): ManagePaymentControlsRequest {
        logger.error(
            "Handling ManagePaymentControls error for messageId: ${request.messageId}: ${error.message}",
            error
        )

        val failureReason = "ManagePaymentControls processing failed: ${error.message}"
        updateRequestStatus(
            request = request,
            status = VisaRequestStatus.FAILED,
            failureReason = failureReason
        )

        // Re-throw the exception to maintain error propagation
        throw RuntimeException(failureReason, error)
    }

    /**
     * Extract payment control rules from GetPaymentControls response.
     * Converts the JsonNode response data to GetPaymentControlsRuleDto list.
     * Works with the actual VISA API response structure.
     * Filters to return only SPV rules as required by the business logic.
     */
    private fun extractRulesFromResponse(getPaymentControlsRequest: GetPaymentControlsRequest): List<GetPaymentControlsRuleDto>? {
        return try {
            getPaymentControlsRequest.retrievedPaymentControlsData?.let { responseData ->
                // The response data contains the actual GetPaymentControlsResponseDto structure
                val responseDto = objectMapper.convertValue(
                    responseData,
                    com.papertrl.visa.infrastructure.outgoing.visa.dto.GetPaymentControlsResponseDto::class.java
                )
                // Filter to return only SPV rules
                responseDto.rules?.filter { rule -> rule.ruleCode == "SPV" }
            }
        } catch (e: Exception) {
            logger.warn("Failed to extract payment control rules from GetPaymentControls response: ${e.message}", e)
            null
        }
    }

    /**
     * Format date string to YYYY/MM/DD format.
     * Handles various input formats and converts them to the required format with forward slashes.
     *
     * @param dateValue The date string to format (can be in various formats)
     * @return Formatted date string in YYYY/MM/DD format
     */
    private fun formatDateToYyyyMmDd(dateValue: String): String {
        val parts = dateValue.split("/")
        return "${parts[2]}-${parts[0]}-${parts[1]}"
    }
}

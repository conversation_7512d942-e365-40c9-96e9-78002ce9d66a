package com.papertrl.visa.domain.service

import com.papertrl.visa.infrastructure.outgoing.visa.dto.GetPaymentControlsRuleDto
import com.papertrl.visa.infrastructure.outgoing.visa.dto.OverrideDto
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Simple service for extracting specific payment control overrides.
 * Replaces the complex PaymentControlRuleMerger with a focused approach
 * that extracts only the required override codes as specified in the expected flow.
 */
@Component
class PaymentControlOverrideExtractor {

    private val logger = LoggerFactory.getLogger(PaymentControlOverrideExtractor::class.java)

    companion object {
        /**
         * The 6 specific override codes required by the expected flow specification.
         */
        private val ALLOWED_OVERRIDE_CODES = setOf(
            "spendLimitAmount",
            "maxAuth", 
            "startDate",
            "amountCurrencyCode",
            "endDate",
            "rangeType"
        )
    }

    /**
     * Extract specific overrides from retrieved payment control rules.
     * Only extracts the 6 override codes specified in the expected flow.
     * Works with the actual VISA API response structure.
     *
     * @param rules The rules array from GetPaymentControls API response
     * @param messageId The message ID for logging purposes
     * @return List of filtered override DTOs containing only the allowed override codes
     */
    fun extractSpecificOverrides(
        rules: List<GetPaymentControlsRuleDto>?,
        messageId: String
    ): List<OverrideDto> {
        if (rules.isNullOrEmpty()) {
            logger.debug("No payment control rules provided for extraction, messageId: $messageId")
            return emptyList()
        }

        logger.debug("Extracting specific overrides from ${rules.size} payment control rules for messageId: $messageId")

        val extractedOverrides = rules.flatMap { rule ->
            rule.overrides
                .filter { override -> override.overrideCode in ALLOWED_OVERRIDE_CODES }
                .map { override ->
                    OverrideDto(
                        sequence = override.sequence,
                        overrideCode = override.overrideCode,
                        overrideValue = override.overrideValue
                    )
                }
        }

        logger.info("Extracted ${extractedOverrides.size} specific overrides for messageId: $messageId")
        return extractedOverrides
    }

    /**
     * Get the list of allowed override codes.
     * Useful for validation and testing purposes.
     */
    fun getAllowedOverrideCodes(): Set<String> {
        return ALLOWED_OVERRIDE_CODES
    }
}

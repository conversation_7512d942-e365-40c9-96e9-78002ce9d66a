package com.papertrl.visa.domain.service

import com.papertrl.visa.domain.model.entity.ProcessPaymentsRequest
import com.papertrl.visa.domain.model.enums.VisaRequestStatus
import com.papertrl.visa.domain.ports.incoming.visa.ProcessPaymentsUseCase
import com.papertrl.visa.domain.ports.outgoing.dbmanager.ProcessPaymentsRepository
import com.papertrl.visa.domain.ports.outgoing.visa.VisaApiClient
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * Primary service for ProcessPayments business logic and orchestration.
 * Implements ProcessPaymentsUseCase interface directly, eliminating unnecessary application service layer.
 * Orchestrates the complete 12-step hexagonal architecture process flow.
 *
 * Architecture: Inbound Port → Domain Service → Outbound Ports
 * - Handles all business logic, validation, and orchestration
 * - Manages Visa API integration and database persistence
 * - Provides comprehensive error handling and logging
 */
@Service
class ProcessPaymentsDomainService(
    private val visaApiClient: VisaApiClient,
    private val processPaymentsRepository: ProcessPaymentsRepository
) : ProcessPaymentsUseCase {

    private val logger = LoggerFactory.getLogger(ProcessPaymentsDomainService::class.java)

    /**
     * Process payment following the 12-step hexagonal architecture flow.
     * Implements ProcessPaymentsUseCase interface with comprehensive error handling and logging.
     *
     * @param request The ProcessPayments domain request
     * @return The processed request with updated status
     * @throws RuntimeException if processing fails after all retries
     */
    override suspend fun processPayment(request: ProcessPaymentsRequest): ProcessPaymentsRequest {
        logger.info("Starting ProcessPayments operation for messageId: ${request.messageId}")

        try {
            val result = processPaymentInternal(request)
            logger.info("Successfully processed payment for messageId: ${request.messageId}, status: ${result.status}")
            return result
        } catch (e: Exception) {
            logger.error("Failed to process payment for messageId: ${request.messageId}", e)
            throw e
        }
    }

    /**
     * Internal method for processing payment.
     * Contains the core 12-step business logic implementation with idempotency check.
     */
    private suspend fun processPaymentInternal(request: ProcessPaymentsRequest): ProcessPaymentsRequest {
        // Step 0: Idempotency check - Query for successful requests with same idempotencyKey
        logger.debug("Checking idempotency for key: ${request.idempotencyKey}")
        val existingSuccessfulRequest = processPaymentsRepository.findByIdempotencyKeyAndStatus(
            request.idempotencyKey,
            VisaRequestStatus.SUCCESS
        )

        if (existingSuccessfulRequest != null) {
            logger.info("Found existing successful request for idempotencyKey: ${request.idempotencyKey}, messageId: ${existingSuccessfulRequest.messageId}")
            logger.info("Returning existing successful result without reprocessing")
            return existingSuccessfulRequest
        } else {
            logger.debug("No existing successful request found for idempotencyKey: ${request.idempotencyKey}, proceeding with processing")
        }

        // Step 1-2: Inbound port → inbound DTO map to domain DTO → central domain logic (validation now handled at inbound port level)
        logger.debug("ProcessPayments request received for processing, messageId: ${request.messageId}, idempotencyKey: ${request.idempotencyKey}")

        // Step 7-8: Domain → domain DTO map to db save DTO → outbound DB save port
        val savedRequest = processPaymentsRepository.save(request)
        logger.debug("ProcessPayments request saved with ID: ${savedRequest.id}, idempotencyKey: ${savedRequest.idempotencyKey}")

        // Step 9-10: Outbound DB save port → db save DTO map to domain DTO → central domain block
        val processingRequest = updateRequestStatus(savedRequest, VisaRequestStatus.PROCESSING)

        try {
            // Step 3-4: Domain → domain DTO map to visa API DTO → outbound visa API port
            logger.debug("Executing ProcessPayments request for message ID: ${request.messageId}")

            // Step 5-6: Outbound visa API port → visa API DTO map to domain DTO → central domain block
            val responseRequest = visaApiClient.sendProcessPaymentsRequest(processingRequest)

            // Step 7-8: Evaluate Visa API response and set appropriate status
            val finalRequest = evaluateAndUpdateRequestStatus(responseRequest)

            logger.info("ProcessPayments completed for message ID: ${finalRequest.messageId}, final status: ${finalRequest.status}")

            // Step 11-12: Domain → domain DTO map to inbound DTO → inbound port
            return finalRequest

        } catch (e: Exception) {
            // Handle error and update status
            handleProcessPaymentsError(processingRequest, e)

            // Re-throw for higher level handling
            throw RuntimeException("ProcessPayments operation failed: ${e.message}", e)
        }
    }


    /**
     * Evaluate Visa API response and set appropriate request status.
     * Since Visa API response fields are no longer stored, we simply mark as SUCCESS.
     */
    private suspend fun evaluateAndUpdateRequestStatus(request: ProcessPaymentsRequest): ProcessPaymentsRequest {
        logger.debug("Evaluating Visa API response for messageId: ${request.messageId}")

        // If we reach this point, the Visa API call was successful
        logger.info("Visa API success for messageId: ${request.messageId}")
        return updateRequestStatus(
            request = request,
            status = VisaRequestStatus.SUCCESS
        )
    }

    /**
     * Update request status and persist to database.
     */
    private suspend fun updateRequestStatus(
        request: ProcessPaymentsRequest,
        status: VisaRequestStatus,
        failureReason: String? = null
    ): ProcessPaymentsRequest {
        val updatedRequest = request.withStatus(
            newStatus = status,
            failureReason = failureReason,
            processedAt = if (status == VisaRequestStatus.SUCCESS) LocalDateTime.now() else null
        )

        return processPaymentsRepository.save(updatedRequest)
    }

    /**
     * Handle ProcessPayments errors with appropriate status updates.
     * Since retry logic is now handled at infrastructure level (VisaApiClientAdapter),
     * any error reaching this level indicates a permanent failure.
     */
    private suspend fun handleProcessPaymentsError(
        request: ProcessPaymentsRequest,
        error: Exception
    ): ProcessPaymentsRequest {
        logger.error("ProcessPayments error for message ID: ${request.messageId}", error)

        val failureReason = "ProcessPayments failed: ${error.message}"

        return updateRequestStatus(
            request = request,
            status = VisaRequestStatus.FAILED,
            failureReason = failureReason
        )
    }
}

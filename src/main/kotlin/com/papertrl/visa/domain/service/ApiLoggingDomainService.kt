package com.papertrl.visa.domain.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.papertrl.visa.domain.model.dto.RequestData
import com.papertrl.visa.domain.model.dto.ResponseData
import com.papertrl.visa.domain.model.entity.ApiRequest
import com.papertrl.visa.domain.model.entity.ApiResponse
import com.papertrl.visa.domain.ports.incoming.dbmanager.ApiLoggingUseCase
import com.papertrl.visa.domain.ports.outgoing.dbmanager.ApiRequestRepository
import com.papertrl.visa.domain.ports.outgoing.dbmanager.ApiResponseRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * Domain service for API logging business logic, orchestration, and use case implementation.
 * Implements ApiLoggingUseCase interface and orchestrates the complete logging process through outbound ports.
 * Now serves as both domain service and Spring-managed bean.
 */
@Service
class ApiLoggingDomainService(
    private val apiRequestRepository: ApiRequestRepository,
    private val apiResponseRepository: ApiResponseRepository,
    private val objectMapper: ObjectMapper
) : ApiLoggingUseCase {

    private val logger = LoggerFactory.getLogger(ApiLoggingDomainService::class.java)

    /**
     * Log only a request.
     * Implements ApiLoggingUseCase interface with error handling and logging.
     */
    override suspend fun logRequest(requestData: RequestData) {
        try {
            logRequestInternal(requestData)
            logger.info("Successfully logged request for messageId: ${requestData.messageId}")
        } catch (e: Exception) {
            logger.error("Failed to log request for messageId: ${requestData.messageId}", e)
            throw e
        }
    }

    /**
     * Update an existing log entry with response data.
     * Implements ApiLoggingUseCase interface with error handling and logging.
     */
    override suspend fun updateWithResponse(messageId: String, responseData: ResponseData) {
        try {
            updateWithResponseInternal(messageId, responseData)
            logger.info("Successfully updated request with response for messageId: $messageId")
        } catch (e: Exception) {
            logger.error("Failed to update request with response for messageId: $messageId", e)
            throw e
        }
    }


    /**
     * Internal method for logging only a request.
     * Contains the core business logic without error handling.
     */
    private suspend fun logRequestInternal(requestData: RequestData) {
        // Step 2: Apply domain logic to prepare model and persist
        val apiRequest = validateAndPrepareRequest(requestData)
        apiRequestRepository.save(apiRequest)
    }

    /**
     * Internal method for updating an existing log entry with response data.
     * Contains the core business logic without error handling.
     */
    private suspend fun updateWithResponseInternal(messageId: String, responseData: ResponseData) {
        // Step 3: Find existing request through outbound port
        val existingRequest = apiRequestRepository.findByMessageId(messageId)

        if (existingRequest != null) {
            // Step 4: Apply domain logic to prepare response model and persist
            val apiResponse = validateAndPrepareResponse(responseData, existingRequest.id)
            apiResponseRepository.save(apiResponse)
        }
    }


    /**
     * Validate and prepare request data for logging.
     * Applies business rules and validation logic.
     */
    private fun validateAndPrepareRequest(requestData: RequestData): ApiRequest {
        // Business validation
        require(requestData.messageId.isNotBlank()) { "Message ID cannot be blank" }
        require(requestData.url.isNotBlank()) { "URL cannot be blank" }
        require(requestData.method.isNotBlank()) { "HTTP method cannot be blank" }

        // Business rule: Validate URL format
        require(isValidUrl(requestData.url)) { "Invalid URL format: ${requestData.url}" }

        // Business rule: Validate HTTP method
        require(isValidHttpMethod(requestData.method)) { "Invalid HTTP method: ${requestData.method}" }

        // Create domain model with business logic applied
        return ApiRequest(
            messageId = requestData.messageId,
            url = requestData.url,
            httpMethod = normalizeHttpMethod(requestData.method),
            requestHeaders = objectMapper.valueToTree(requestData.headers),
            requestBody = requestData.body?.let { objectMapper.readTree(it) },
            requestTimestamp = requestData.timestamp
        )
    }

    /**
     * Validate and prepare response data for logging.
     * Applies business rules and validation logic.
     */
    private fun validateAndPrepareResponse(responseData: ResponseData, requestId: Long?): ApiResponse {
        // Business validation
        require(responseData.messageId.isNotBlank()) { "Message ID cannot be blank" }
        require(responseData.statusCode > 0) { "Status code must be positive" }

        // Business rule: Validate status code range
        require(isValidStatusCode(responseData.statusCode)) { "Invalid HTTP status code: ${responseData.statusCode}" }

        // Create domain model with business logic applied
        return ApiResponse(
            apiRequestId = requestId,
            responseStatusCode = responseData.statusCode,
            responseHeaders = objectMapper.valueToTree(responseData.headers),
            responseBody = responseData.body?.let { objectMapper.readTree(it) },
            responseTimestamp = responseData.timestamp
        )
    }

    // Private helper methods for business logic

    private fun isValidUrl(url: String): Boolean {
        return try {
            java.net.URI.create(url)
            url.startsWith("http://") || url.startsWith("https://")
        } catch (_: Exception) {
            false
        }
    }

    private fun isValidHttpMethod(method: String): Boolean {
        val validMethods = setOf("GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS")
        return validMethods.contains(method.uppercase())
    }

    private fun isValidStatusCode(statusCode: Int): Boolean {
        return statusCode in 100..599
    }

    private fun normalizeHttpMethod(method: String): String {
        return method.uppercase()
    }

}

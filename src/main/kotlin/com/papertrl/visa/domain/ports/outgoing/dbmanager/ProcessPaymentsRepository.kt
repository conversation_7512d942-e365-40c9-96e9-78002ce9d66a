package com.papertrl.visa.domain.ports.outgoing.dbmanager

import com.papertrl.visa.domain.model.entity.ProcessPaymentsRequest
import com.papertrl.visa.domain.model.enums.VisaRequestStatus

/**
 * Repository interface for ProcessPaymentsRequest domain model.
 * Defines operations for persisting and retrieving payment processing data.
 * Uses only domain models - no infrastructure dependencies.
 */
interface ProcessPaymentsRepository {

    /**
     * Save a ProcessPayments request.
     *
     * @param request The request to save
     * @return The saved request with updated ID and timestamps
     */
    suspend fun save(request: ProcessPaymentsRequest): ProcessPaymentsRequest

    /**
     * Find a ProcessPayments request by message ID.
     *
     * @param messageId The message ID to search for
     * @return The request if found, null otherwise
     */
    suspend fun findByMessageId(messageId: String): ProcessPaymentsRequest?

    /**
     * Find a ProcessPayments request by idempotency key and status.
     *
     * @param idempotencyKey The idempotency key to search for
     * @param status The status to filter by
     * @return The request if found, null otherwise
     */
    suspend fun findByIdempotencyKeyAndStatus(
        idempotencyKey: String,
        status: VisaRequestStatus
    ): ProcessPaymentsRequest?

    /**
     * Find a ProcessPayments request by ID.
     *
     * @param id The ID to search for
     * @return The request if found, null otherwise
     */
    suspend fun findById(id: Long): ProcessPaymentsRequest?


    /**
     * Update the status of a ProcessPayments request.
     *
     * @param messageId The message ID of the request to update
     * @param status The new status
     * @param failureReason Optional failure reason
     * @return The updated request if found, null otherwise
     */
    suspend fun updateStatus(
        messageId: String,
        status: VisaRequestStatus,
        failureReason: String? = null
    ): ProcessPaymentsRequest?

    /**
     * Update VISA response fields for a ProcessPayments request.
     *
     * @param messageId The message ID of the request to update
     * @param accountNumber The account number from VISA response
     * @param expirationDate The expiration date from VISA response
     * @return The updated request if found, null otherwise
     */
    suspend fun updateVisaResponseFields(
        messageId: String,
        accountNumber: String?,
        expirationDate: String?
    ): ProcessPaymentsRequest?

    /**
     * Find all ProcessPayments requests.
     * Used primarily for testing purposes.
     *
     * @return List of all requests
     */
    suspend fun findAll(): List<ProcessPaymentsRequest>

    /**
     * Delete all ProcessPayments requests.
     * Used primarily for testing purposes.
     */
    suspend fun deleteAll()
}

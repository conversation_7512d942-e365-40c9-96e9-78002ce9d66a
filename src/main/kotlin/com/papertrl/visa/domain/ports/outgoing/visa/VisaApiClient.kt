package com.papertrl.visa.domain.ports.outgoing.visa

import com.papertrl.visa.domain.model.entity.GetPaymentControlsRequest
import com.papertrl.visa.domain.model.entity.ManagePaymentControlsRequest
import com.papertrl.visa.domain.model.entity.ProcessPaymentsRequest
import com.papertrl.visa.infrastructure.outgoing.visa.dto.PaymentControlDetailDto

/**
 * Outbound port interface for Visa API client operations.
 * Defines the contract for external Visa API integration.
 * Uses only domain models - no infrastructure dependencies.
 */
interface VisaApiClient {

    /**
     * Send ProcessPayments request to Visa API.
     *
     * @param request The request to send
     * @return The processed request with Visa API response data
     * @throws RuntimeException if API call fails
     */
    suspend fun sendProcessPaymentsRequest(request: ProcessPaymentsRequest): ProcessPaymentsRequest

    /**
     * Send ManagePaymentControls request to Visa API.
     *
     * @param request The request to send
     * @param paymentControlDetails The payment control details to send
     * @return The processed request with Visa API response data
     * @throws RuntimeException if API call fails
     */
    suspend fun sendManagePaymentControlsRequest(
        request: ManagePaymentControlsRequest,
        paymentControlDetails: List<PaymentControlDetailDto>
    ): ManagePaymentControlsRequest

    /**
     * Send ManagePaymentControls request to Visa API (legacy method).
     *
     * @param request The request to send
     * @return The processed request with Visa API response data
     * @throws RuntimeException if API call fails
     * @deprecated Use the version that accepts paymentControlDetails as a separate parameter
     */
    @Deprecated("Use sendManagePaymentControlsRequest(request, paymentControlDetails) instead")
    suspend fun sendManagePaymentControlsRequest(request: ManagePaymentControlsRequest): ManagePaymentControlsRequest

    /**
     * Send GetPaymentControls request to Visa API.
     *
     * @param request The request to send
     * @return The processed request with Visa API response data and retrieved payment controls
     * @throws RuntimeException if API call fails
     */
    suspend fun sendGetPaymentControlsRequest(request: GetPaymentControlsRequest): GetPaymentControlsRequest
}
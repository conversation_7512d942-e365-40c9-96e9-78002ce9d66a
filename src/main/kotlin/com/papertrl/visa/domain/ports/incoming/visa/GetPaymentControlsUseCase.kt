package com.papertrl.visa.domain.ports.incoming.visa

import com.papertrl.visa.domain.model.entity.GetPaymentControlsRequest

/**
 * Inbound port interface for GetPaymentControls business operations.
 * Defines the contract for Visa GetPaymentControls API integration business logic.
 * Uses only domain models - no infrastructure dependencies.
 */
interface GetPaymentControlsUseCase {

    /**
     * Process a payment controls retrieval request through Visa GetPaymentControls API with retry logic.
     * Follows the 12-step process flow for hexagonal architecture.
     *
     * @param request The GetPaymentControls domain request
     * @return The processed request with updated status and retrieved data
     * @throws RuntimeException if processing fails after all retries
     */
    suspend fun getPaymentControls(request: GetPaymentControlsRequest): GetPaymentControlsRequest
}

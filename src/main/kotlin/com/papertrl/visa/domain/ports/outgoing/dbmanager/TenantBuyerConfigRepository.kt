package com.papertrl.visa.domain.ports.outgoing.dbmanager

import com.papertrl.visa.domain.model.entity.TenantBuyerConfig

/**
 * Outbound port interface for TenantBuyerConfig repository operations.
 * Defines the contract for tenant buyer configuration data access.
 * Uses only domain models - no infrastructure dependencies.
 * Simplified for admin-managed tenant configurations.
 */
interface TenantBuyerConfigRepository {

    /**
     * Find active tenant buyer configuration by tenant ID.
     * @param tenantId The tenant identifier
     * @return The active tenant buyer configuration or null if not found
     */
    suspend fun findActiveByTenantId(tenantId: String): TenantBuyerConfig?
}

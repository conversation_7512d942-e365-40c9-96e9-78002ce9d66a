package com.papertrl.visa.domain.ports.outgoing.dbmanager

import com.papertrl.visa.domain.model.entity.ManagePaymentControlsRequest
import com.papertrl.visa.domain.model.enums.VisaRequestStatus

/**
 * Repository interface for ManagePaymentControlsRequest domain model.
 * Defines operations for persisting and retrieving payment control management data.
 * Uses only domain models - no infrastructure dependencies.
 */
interface ManagePaymentControlsRepository {

    /**
     * Save a ManagePaymentControls request.
     *
     * @param request The request to save
     * @return The saved request with updated ID and timestamps
     */
    suspend fun save(request: ManagePaymentControlsRequest): ManagePaymentControlsRequest

    /**
     * Find a ManagePaymentControls request by message ID.
     *
     * @param messageId The message ID to search for
     * @return The request if found, null otherwise
     */
    suspend fun findByMessageId(messageId: String): ManagePaymentControlsRequest?

    /**
     * Find a ManagePaymentControls request by idempotency key and status.
     *
     * @param idempotencyKey The idempotency key to search for
     * @param status The status to filter by
     * @return The request if found, null otherwise
     */
    suspend fun findByIdempotencyKeyAndStatus(
        idempotencyKey: String,
        status: VisaRequestStatus
    ): ManagePaymentControlsRequest?

    /**
     * Find a ManagePaymentControls request by ID.
     *
     * @param id The ID to search for
     * @return The request if found, null otherwise
     */
    suspend fun findById(id: Long): ManagePaymentControlsRequest?

    /**
     * Update the status of a ManagePaymentControls request.
     *
     * @param messageId The message ID of the request to update
     * @param status The new status
     * @param failureReason Optional failure reason
     * @return The updated request if found, null otherwise
     */
    suspend fun updateStatus(
        messageId: String,
        status: VisaRequestStatus,
        failureReason: String? = null
    ): ManagePaymentControlsRequest?

    /**
     * Find all ManagePaymentControls requests.
     * Used primarily for testing purposes.
     *
     * @return List of all requests
     */
    suspend fun findAll(): List<ManagePaymentControlsRequest>

    /**
     * Delete all ManagePaymentControls requests.
     * Used primarily for testing purposes.
     */
    suspend fun deleteAll()
}

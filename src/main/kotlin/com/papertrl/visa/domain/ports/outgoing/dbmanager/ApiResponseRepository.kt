package com.papertrl.visa.domain.ports.outgoing.dbmanager

import com.papertrl.visa.domain.model.entity.ApiResponse

/**
 * Repository interface for ApiResponse domain model.
 * Defines operations for persisting and retrieving API response data.
 */
interface ApiResponseRepository {

    /**
     * Save an API response.
     */
    suspend fun save(apiResponse: ApiResponse): ApiResponse

    /**
     * Find an API response by ID.
     */
    suspend fun findById(id: Long): ApiResponse?

    /**
     * Find API responses by request ID.
     */
    suspend fun findByApiRequestId(apiRequestId: Long): List<ApiResponse>


}

package com.papertrl.visa.domain.ports.incoming.visa

import com.papertrl.visa.domain.model.entity.ManagePaymentControlsRequest

/**
 * Inbound port interface for ManagePaymentControls business operations.
 * Defines the contract for Visa ManagePaymentControls API integration business logic.
 * Uses only domain models - no infrastructure dependencies.
 */
interface ManagePaymentControlsUseCase {

    /**
     * Process a payment controls request through Visa ManagePaymentControls API with retry logic.
     * Follows the 12-step process flow for hexagonal architecture.
     *
     * @param request The ManagePaymentControls domain request
     * @return The processed request with updated status
     * @throws RuntimeException if processing fails after all retries
     */
    suspend fun managePaymentControls(request: ManagePaymentControlsRequest): ManagePaymentControlsRequest
}

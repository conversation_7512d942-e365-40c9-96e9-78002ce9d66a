package com.papertrl.visa.domain.ports.incoming.dbmanager

import com.papertrl.visa.domain.model.dto.RequestData
import com.papertrl.visa.domain.model.dto.ResponseData

/**
 * Use case interface for API request/response logging.
 * Defines the contract for logging HTTP interactions in the domain layer.
 */
interface ApiLoggingUseCase {

    /**
     * Log only a request (when response is not yet available).
     */
    suspend fun logRequest(requestData: RequestData)

    /**
     * Update an existing log entry with response data.
     */
    suspend fun updateWithResponse(messageId: String, responseData: ResponseData)
}
package com.papertrl.visa.domain.ports.outgoing.dbmanager

import com.papertrl.visa.domain.model.entity.ApiRequest

/**
 * Repository interface for ApiRequest domain model.
 * Defines operations for persisting and retrieving API request data.
 */
interface ApiRequestRepository {

    /**
     * Save an API request.
     */
    suspend fun save(apiRequest: ApiRequest): ApiRequest

    /**
     * Find an API request by message ID.
     */
    suspend fun findByMessageId(messageId: String): ApiRequest?

    /**
     * Find an API request by ID.
     */
    suspend fun findById(id: Long): ApiRequest?
}

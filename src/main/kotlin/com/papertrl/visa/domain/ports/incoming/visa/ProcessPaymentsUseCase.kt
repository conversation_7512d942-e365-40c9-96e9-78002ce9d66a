package com.papertrl.visa.domain.ports.incoming.visa

import com.papertrl.visa.domain.model.entity.ProcessPaymentsRequest

/**
 * Inbound port interface for ProcessPayments business operations.
 * Defines the contract for Visa ProcessPayments API integration business logic.
 * Uses only domain models - no infrastructure dependencies.
 */
interface ProcessPaymentsUseCase {

    /**
     * Process a payment request through Visa ProcessPayments API with retry logic.
     * Follows the 12-step process flow for hexagonal architecture.
     *
     * @param request The ProcessPayments domain request
     * @return The processed request with updated status
     * @throws RuntimeException if processing fails after all retries
     */
    suspend fun processPayment(request: ProcessPaymentsRequest): ProcessPaymentsRequest
}
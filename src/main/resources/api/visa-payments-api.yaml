openapi: 3.0.3
info:
  title: Visa Payments API
  description: API for processing payments through Visa API
  version: 1.0.0
  contact:
    name: PaperTRL
servers:
  - url: /api/v1
    description: Base API path
tags:
  - name: visa-payments
    description: Visa Payments Operations
paths:
  /visa/payments/process:
    post:
      tags:
        - visa-payments
      summary: Process a payment request
      description: Process a payment request with input validation
      operationId: processPayment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentTransactionDto'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PapertrlTransactionResponseDto'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /visa/payments/manage-controls:
    post:
      tags:
        - visa-payments
      summary: Manage payment controls
      description: Manage payment controls with input validation
      operationId: managePaymentControls
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentTransactionDto'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PapertrlTransactionResponseDto'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'


components:
  schemas:
    PaymentTransactionDto:
      type: object
      required:
        - idempotencyKey
        - tenantId
      properties:
        recordId:
          type: integer
          format: int32
          nullable: true
          description: Record identifier
        id:
          type: integer
          format: int32
          nullable: true
          description: Transaction identifier
        txnRef:
          type: string
          nullable: true
          description: Transaction reference
        txnType:
          type: integer
          format: int32
          nullable: true
          description: Transaction type
        txnAmount:
          type: number
          format: decimal
          nullable: true
          description: Transaction amount
        comment:
          type: string
          nullable: true
          description: Transaction comment
        tpId:
          type: string
          nullable: true
          description: Third party identifier
        paymentDate:
          type: string
          format: date-time
          nullable: true
          description: Payment date
        payeeName:
          type: string
          nullable: true
          description: Payee name
        correlationId:
          type: string
          nullable: true
          description: Correlation identifier
        idempotencyKey:
          type: string
          description: Idempotency key for the request
        credentials:
          type: string
          nullable: true
          description: Credentials
        attempt:
          type: integer
          format: int32
          nullable: true
          description: Attempt number
        tenantId:
          type: string
          description: Tenant identifier for multi-tenant support
        osn:
          type: string
          nullable: true
          description: OSN identifier
        clientId:
          type: string
          nullable: true
          description: Client identifier
        checkNumber:
          type: string
          nullable: true
          description: Check number
        topUpAmount:
          type: number
          format: decimal
          default: 0
          description: Top up amount
        expireOn:
          type: string
          format: date-time
          nullable: true
          description: Expiration date
        createdOn:
          type: string
          format: date-time
          nullable: true
          description: Creation date
        effectiveUntil:
          type: string
          format: date-time
          nullable: true
          description: Effective until date
        precisePay:
          type: boolean
          nullable: true
          description: Precise pay flag
        customerId:
          type: string
          nullable: true
          description: Customer identifier
        providerId:
          type: integer
          format: int32
          nullable: true
          description: Provider identifier
        recipientDetail:
          $ref: '#/components/schemas/PaymentRecipientDetailDto'
        bankAccountMst:
          $ref: '#/components/schemas/BankAccountMstDto'
        requesterDetail:
          $ref: '#/components/schemas/PaymentRequestorDetailDto'
        transactionSummaryList:
          type: array
          items:
            $ref: '#/components/schemas/PaymentTransactionSummaryDto'
          nullable: true
          description: List of transaction summaries

    PaymentTransactionSummaryDto:
      type: object
      properties:
        id:
          type: integer
          format: int32
          nullable: true
          description: Summary identifier
        billNumber:
          type: string
          nullable: true
          description: Bill number
        paymentDateStr:
          type: string
          nullable: true
          description: Payment date as string
        ref:
          type: string
          nullable: true
          description: Reference
        txnAmount:
          type: number
          format: decimal
          nullable: true
          description: Transaction amount
        billAmount:
          type: number
          format: decimal
          nullable: true
          description: Bill amount
        transaction:
          $ref: '#/components/schemas/PaymentTransactionDto'
        comment:
          type: string
          nullable: true
          description: Comment

    PaymentRecipientDetailDto:
      type: object
      properties:
        recordId:
          type: integer
          format: int32
          nullable: true
          description: Record identifier
        id:
          type: integer
          format: int32
          nullable: true
          description: Recipient identifier
        recipientType:
          type: string
          nullable: true
          description: Recipient type
        companyName:
          type: string
          nullable: true
          description: Company name
        recipientFirstName:
          type: string
          nullable: true
          description: Recipient first name
        recipientLastName:
          type: string
          nullable: true
          description: Recipient last name
        accountType:
          type: string
          nullable: true
          description: Account type
        recipientToken:
          type: string
          nullable: true
          description: Recipient token
        vendorId:
          type: integer
          format: int32
          nullable: true
          description: Vendor identifier
        userId:
          type: string
          nullable: true
          description: User identifier
        phone:
          type: string
          nullable: true
          description: Phone number
        email:
          type: string
          nullable: true
          description: Email address
        recipientIdentification:
          type: string
          nullable: true
          description: Recipient identification (vendor code or employee number)
        addressLine1:
          type: string
          nullable: true
          description: Address line 1
        addressLine2:
          type: string
          nullable: true
          description: Address line 2
        country:
          type: string
          nullable: true
          description: Country
        city:
          type: string
          nullable: true
          description: City
        zipcode:
          type: string
          nullable: true
          description: Zip code
        addressState:
          type: string
          nullable: true
          description: State
        accountNumber:
          type: string
          nullable: true
          description: Account number
        routingNumber:
          type: string
          nullable: true
          description: Routing number

    BankAccountMstDto:
      type: object
      properties:
        recordId:
          type: integer
          format: int32
          nullable: true
          description: Record identifier
        id:
          type: integer
          format: int64
          nullable: true
          description: Bank account identifier
        routingNumber:
          type: string
          nullable: true
          description: Routing number
        bankId:
          type: integer
          format: int32
          nullable: true
          description: Bank identifier
        accountNo:
          type: string
          nullable: true
          description: Account number
        transitNo:
          type: string
          nullable: true
          description: Transit number
        accountNicName:
          type: string
          nullable: true
          description: Account nickname
        companyName:
          type: string
          nullable: true
          description: Company name
        fileUrl:
          type: string
          nullable: true
          description: File URL
        companyId:
          type: string
          nullable: true
          description: Company identifier
        vCardAccountId:
          type: string
          nullable: true
          description: V-Card account identifier
        dCardAccountId:
          type: string
          nullable: true
          description: D-Card account identifier

    PaymentRequestorDetailDto:
      type: object
      properties:
        id:
          type: integer
          format: int32
          nullable: true
          description: Requestor identifier
        name:
          type: string
          nullable: true
          description: Requestor name
        addressLine1:
          type: string
          nullable: true
          description: Address line 1
        addressLine2:
          type: string
          nullable: true
          description: Address line 2
        country:
          type: string
          nullable: true
          description: Country
        city:
          type: string
          nullable: true
          description: City
        zipcode:
          type: string
          nullable: true
          description: Zip code
        state:
          type: string
          nullable: true
          description: State

    PaymentDto:
      type: object
      required:
        - paymentExpiryDate
        - invoices
        - supplier
        - accountType
        - currencyCode
        - paymentGrossAmount
        - paymentType
      properties:
        paymentExpiryDate:
          type: string
          description: Payment expiry date
        invoices:
          type: array
          items:
            $ref: '#/components/schemas/InvoiceDto'
          description: List of invoices
        supplier:
          $ref: '#/components/schemas/SupplierDto'
        accountType:
          type: integer
          format: int32
          minimum: 1
          description: Account type
        currencyCode:
          type: string
          minLength: 3
          maxLength: 3
          description: Currency code (3 characters)
        paymentGrossAmount:
          type: number
          format: decimal
          minimum: 0.01
          maximum: 999999.99
          description: Payment gross amount
        paymentType:
          type: string
          description: Payment type

    InvoiceDto:
      type: object
      required:
        - invoiceNumber
        - invoiceAmount
        - invoiceDate
      properties:
        invoiceNumber:
          type: string
          description: Invoice number
        invoiceAmount:
          type: number
          format: decimal
          minimum: 0.01
          description: Invoice amount
        invoiceDate:
          type: string
          description: Invoice date

    SupplierDto:
      type: object
      required:
        - supplierName
        - supplierID
        - supplierCity
        - supplierAddressLine1
        - supplierCountryCode
        - supplierPostalCode
        - primaryEmailAddress
      properties:
        supplierName:
          type: string
          description: Supplier name
        alternateEmailAddresses:
          type: array
          items:
            $ref: '#/components/schemas/AlternateEmailAddressDto'
          nullable: true
          description: List of alternate email addresses
        emailNotes:
          type: string
          nullable: true
          maxLength: 1000
          description: Email notes
        supplierID:
          type: integer
          format: int64
          minimum: 1
          description: Supplier ID
        supplierCity:
          type: string
          description: Supplier city
        supplierAddressLine2:
          type: string
          nullable: true
          description: Supplier address line 2
        supplierAddressLine1:
          type: string
          description: Supplier address line 1
        supplierState:
          type: string
          nullable: true
          description: Supplier state
        supplierCountryCode:
          type: string
          description: Supplier country code
        supplierPostalCode:
          type: integer
          format: int32
          description: Supplier postal code
        primaryEmailAddress:
          type: string
          format: email
          maxLength: 255
          description: Primary email address

    AlternateEmailAddressDto:
      type: object
      required:
        - alternateEmailAddress
      properties:
        alternateEmailAddress:
          type: string
          format: email
          description: Alternate email address
    PapertrlTransactionResponseDto:
      type: object
      properties:
        id:
          type: integer
          format: int32
          nullable: true
          description: Transaction identifier
        tpTxnId:
          type: string
          description: Third party transaction ID (maps to messageId)
        txnType:
          type: integer
          format: int32
          nullable: true
          description: Transaction type
        canceled:
          type: boolean
          default: false
          description: Whether transaction is canceled
        tpStatus:
          type: string
          nullable: true
          description: Third party status
        failReason:
          type: string
          nullable: true
          description: Failure reason if transaction failed
        httpStatusCode:
          type: integer
          format: int32
          nullable: true
          description: HTTP status code
        clientInputDataIssue:
          type: boolean
          default: false
          description: Whether there is a client input data issue
        cardDetails:
          $ref: '#/components/schemas/PapertrlCardDetailsDto'
          nullable: true
          description: Card details information
    PapertrlCardDetailsDto:
      type: object
      properties:
        credentials:
          type: string
          nullable: true
          description: Card credentials
        number:
          type: string
          nullable: true
          description: Card number
        effectUntil:
          type: string
          format: date
          nullable: true
          description: Effective until date
        expireOn:
          type: string
          format: date
          nullable: true
          description: Expiration date
        status:
          type: string
          nullable: true
          description: Card status
    ErrorResponse:
      type: object
      properties:
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the error
        status:
          type: integer
          format: int32
          description: HTTP status code
        error:
          type: string
          description: Error type
        message:
          type: string
          description: Error message
        path:
          type: string
          description: Request path



    PaymentControlDetailDto:
      type: object
      required:
        - rulesSet
        - endDate
        - mcgRuleAction
        - timeZone
        - startDate
      properties:
        rulesSet:
          type: array
          items:
            $ref: '#/components/schemas/RulesSetDto'
          description: List of rules sets
        endDate:
          type: string
          pattern: '^\\d{4}-\\d{2}-\\d{2}$'
          description: End date in YYYY-MM-DD format
        mcgRuleAction:
          type: string
          maxLength: 20
          description: MCG rule action
        timeZone:
          type: string
          maxLength: 10
          description: Time zone
        startDate:
          type: string
          pattern: '^\\d{4}-\\d{2}-\\d{2}$'
          description: Start date in YYYY-MM-DD format

    RulesSetDto:
      type: object
      required:
        - action
        - rules
      properties:
        action:
          type: string
          maxLength: 5
          description: Action for the rules set
        rules:
          type: array
          items:
            $ref: '#/components/schemas/RuleDto'
          description: List of rules

    RuleDto:
      type: object
      required:
        - ruleCode
        - overrides
      properties:
        ruleCode:
          type: string
          maxLength: 10
          description: Rule code
        overrides:
          type: array
          items:
            $ref: '#/components/schemas/OverrideDto'
          description: List of overrides

    OverrideDto:
      type: object
      required:
        - sequence
        - overrideCode
        - overrideValue
      properties:
        sequence:
          type: string
          maxLength: 5
          description: Sequence number
        overrideCode:
          type: string
          maxLength: 50
          description: Override code
        overrideValue:
          type: string
          maxLength: 100
          description: Override value

    ManagePaymentControlsResponseDto:
      type: object
      required:
        - success
        - message
        - messageId
        - status
        - timestamp
      properties:
        success:
          type: boolean
          description: Whether the operation was successful
        message:
          type: string
          description: Response message
        messageId:
          type: string
          description: Message ID from the request
        status:
          type: string
          enum: [PENDING, PROCESSING, COMPLETED, FAILED]
          description: Processing status
        data:
          $ref: '#/components/schemas/ManagePaymentControlsResponseDataDto'
        error:
          $ref: '#/components/schemas/ManagePaymentControlsErrorDetailsDto'
        timestamp:
          type: string
          format: date-time
          description: Response timestamp

    ManagePaymentControlsResponseDataDto:
      type: object
      required:
        - messageId
        - accountNumber
        - status
      properties:
        messageId:
          type: string
          description: Message ID
        accountNumber:
          type: string
          description: Account number
        status:
          type: string
          description: Processing status

    ManagePaymentControlsErrorDetailsDto:
      type: object
      required:
        - code
        - description
      properties:
        code:
          type: string
          description: Error code
        description:
          type: string
          description: Error description
        details:
          type: string
          nullable: true
          description: Additional error details



-- Create tenant_buyer_config table for tenant-specific buyer ID management
-- Stores mapping between tenant IDs and their corresponding Visa API buyer IDs
-- Supports multi-tenant architecture with dynamic buyer ID lookup
-- Simplified for admin-managed data

CREATE TABLE tenant_buyer_config (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(100) NOT NULL UNIQUE,
    buyer_id VARCHAR(100) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true
);

-- Add comments for documentation
COMMENT ON TABLE tenant_buyer_config IS 'Stores tenant-specific buyer ID configurations for Visa API integration (admin-managed)';
COMMENT ON COLUMN tenant_buyer_config.tenant_id IS 'Unique tenant identifier for multi-tenant support';
COMMENT ON COLUMN tenant_buyer_config.buyer_id IS 'Visa API buyer ID for the specific tenant';
COMMENT ON COLUMN tenant_buyer_config.is_active IS 'Flag to enable/disable tenant configuration';

-- Create indexes for optimal query performance
CREATE UNIQUE INDEX idx_tenant_buyer_config_tenant_id ON tenant_buyer_config(tenant_id);
CREATE INDEX idx_tenant_buyer_config_active ON tenant_buyer_config(is_active);

-- Migration V005: Create ManagePaymentControls requests table for Visa API integration
-- This table tracks ManagePaymentControls API requests with status and retry management

CREATE TABLE manage_payment_controls_requests (
    id BIGSERIAL PRIMARY KEY,
    message_id VARCHAR(100) NOT NULL UNIQUE,
    idempotency_key VARCHAR(100) NOT NULL,
    tenant_id VARCHAR(100) NOT NULL,
    client_id VARCHAR(100) NOT NULL,
    buyer_id VARCHAR(100) NOT NULL,
    account_number VARCHAR(50) NOT NULL,
    expire_on DATE NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    failure_reason VARCHAR(1000),
    processed_at TIMESTAMP,
    created_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM',
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM',
    updated_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Optimized indexes for ManagePaymentControls requests
CREATE UNIQUE INDEX idx_manage_payment_controls_message_id ON manage_payment_controls_requests(message_id);
CREATE INDEX idx_manage_payment_controls_idempotency_key ON manage_payment_controls_requests(idempotency_key);
CREATE INDEX idx_manage_payment_controls_tenant_id ON manage_payment_controls_requests(tenant_id);
CREATE INDEX idx_manage_payment_controls_status ON manage_payment_controls_requests(status);
CREATE INDEX idx_manage_payment_controls_client_id ON manage_payment_controls_requests(client_id);
CREATE INDEX idx_manage_payment_controls_buyer_id ON manage_payment_controls_requests(buyer_id);
CREATE INDEX idx_manage_payment_controls_account_number ON manage_payment_controls_requests(account_number);

-- Composite indexes for common query patterns
CREATE INDEX idx_manage_payment_controls_status_created ON manage_payment_controls_requests(status, created_date);
CREATE INDEX idx_manage_payment_controls_client_status ON manage_payment_controls_requests(client_id, status);
CREATE INDEX idx_manage_payment_controls_idempotency_status ON manage_payment_controls_requests(idempotency_key, status);

-- Comments for documentation
COMMENT ON TABLE manage_payment_controls_requests IS 'Tracks ManagePaymentControls API requests with status and retry management';
COMMENT ON COLUMN manage_payment_controls_requests.message_id IS 'Unique message ID from Visa ManagePaymentControls API';
COMMENT ON COLUMN manage_payment_controls_requests.idempotency_key IS 'Idempotency key for safe retry operations';
COMMENT ON COLUMN manage_payment_controls_requests.tenant_id IS 'Tenant identifier for multi-tenant support';
COMMENT ON COLUMN manage_payment_controls_requests.status IS 'Processing status: PENDING, PROCESSING, SUCCESS, RETRY, FAILED';
COMMENT ON COLUMN manage_payment_controls_requests.client_id IS 'Client identifier for the request';
COMMENT ON COLUMN manage_payment_controls_requests.account_number IS 'Account number for payment control management';
COMMENT ON COLUMN manage_payment_controls_requests.buyer_id IS 'Buyer identifier for the request';
COMMENT ON COLUMN manage_payment_controls_requests.expire_on IS 'Expiration date for payment control management';
COMMENT ON COLUMN manage_payment_controls_requests.failure_reason IS 'Detailed failure reason for failed requests';

-- Add check constraint for status enum values
ALTER TABLE manage_payment_controls_requests ADD CONSTRAINT chk_manage_payment_controls_status 
    CHECK (status IN ('PENDING', 'PROCESSING', 'SUCCESS', 'RETRY', 'FAILED'));

-- Add check constraint for account number to be non-empty
ALTER TABLE manage_payment_controls_requests ADD CONSTRAINT chk_manage_payment_controls_account_number 
    CHECK (LENGTH(TRIM(account_number)) > 0);

-- Add check constraint for idempotency key to be non-empty
ALTER TABLE manage_payment_controls_requests ADD CONSTRAINT chk_manage_payment_controls_idempotency_key 
    CHECK (LENGTH(TRIM(idempotency_key)) > 0);

-- Add check constraint for tenant ID to be non-empty
ALTER TABLE manage_payment_controls_requests ADD CONSTRAINT chk_manage_payment_controls_tenant_id 
    CHECK (LENGTH(TRIM(tenant_id)) > 0);

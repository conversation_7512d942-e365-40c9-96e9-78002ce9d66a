-- Create api_request table for logging outgoing API requests
-- Stores comprehensive request data for audit trail and monitoring
-- Excludes userAgent, ipAddress, and sessionId fields as per requirements

CREATE TABLE api_request (
    id BIGSERIAL PRIMARY KEY,
    message_id VARCHAR(100) NOT NULL UNIQUE,
    url VARCHAR(500) NOT NULL,
    http_method VARCHAR(10) NOT NULL,
    request_headers JSONB,
    request_body JSONB,
    request_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    error_message VARCHAR(2000),
    request_status VARCHAR(20) NOT NULL DEFAULT 'SUCCESS',
    retry_count INTEGER NOT NULL DEFAULT 0
);

-- Add comments for documentation
COMMENT ON TABLE api_request IS 'Stores outgoing API request data for audit trail and monitoring';
COMMENT ON COLUMN api_request.message_id IS 'Unique identifier for request correlation';
COMMENT ON COLUMN api_request.url IS 'Target URL for the API request';
COMMENT ON COLUMN api_request.http_method IS 'HTTP method (GET, POST, PUT, DELETE, etc.)';
COMMENT ON COLUMN api_request.request_headers IS 'JSONB representation of request headers';
COMMENT ON COLUMN api_request.request_body IS 'JSONB request payload data';
COMMENT ON COLUMN api_request.request_timestamp IS 'Timestamp when request was made';
COMMENT ON COLUMN api_request.error_message IS 'Detailed error information when requests fail (max 2000 chars)';
COMMENT ON COLUMN api_request.request_status IS 'Request outcome status: SUCCESS, FAILED, RETRYING';
COMMENT ON COLUMN api_request.retry_count IS 'Number of retry attempts made for this request';

-- Create api_response table for logging incoming API responses
-- Stores comprehensive response data linked to requests via foreign key
-- Supports webhook responses (api_request_id can be null)
-- Excludes userAgent, ipAddress, and sessionId fields as per requirements

CREATE TABLE api_response (
    id BIGSERIAL PRIMARY KEY,
    api_request_id BIGINT,
    response_status_code INTEGER NOT NULL,
    response_headers JSON<PERSON>,
    response_body JSONB,
    response_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Foreign key constraint (optional since webhook responses can have null api_request_id)
    CONSTRAINT fk_api_response_request
        FOREIGN KEY (api_request_id)
        REFERENCES api_request(id)
        ON DELETE CASCADE
);

-- Add comments for documentation
COMMENT ON TABLE api_response IS 'Stores incoming API response data linked to requests';
COMMENT ON COLUMN api_response.api_request_id IS 'Reference to associated request (null for webhooks)';
COMMENT ON COLUMN api_response.response_status_code IS 'HTTP response status code';
COMMENT ON COLUMN api_response.response_headers IS 'JSONB representation of response headers';
COMMENT ON COLUMN api_response.response_body IS 'JSONB response payload data';
COMMENT ON COLUMN api_response.response_timestamp IS 'Timestamp when response was received';

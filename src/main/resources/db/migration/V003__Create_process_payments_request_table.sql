-- Create process_payments_request table for storing ProcessPayments requests
-- Stores comprehensive payment processing data for audit trail and monitoring
-- Uses JSONB for JSON data columns for better PostgreSQL performance

CREATE TABLE process_payments_request (
    id BIGSERIAL PRIMARY KEY,
    message_id VARCHAR(100) NOT NULL UNIQUE,
    idempotency_key VARCHAR(255) NOT NULL,
    tenant_id VARCHAR(100) NOT NULL,
    action_type INTEGER NOT NULL,
    client_id VARCHAR(100) NOT NULL,
    buyer_id VARCHAR(100) NOT NULL,
    payment_expiry_date VARCHAR(20) NOT NULL,
    account_type INTEGER NOT NULL,
    currency_code VARCHAR(3) NOT NULL,
    payment_gross_amount DECIMAL(19,2) NOT NULL,
    payment_type VARCHAR(50) NOT NULL,
    supplier_name VARCHAR(255) NOT NULL,
    supplier_id BIGINT NOT NULL,
    supplier_address_line2 VARCHAR(255),
    supplier_address_line1 VARCHAR(255) NOT NULL,
    supplier_city VARCHAR(100) NOT NULL,
    supplier_state VARCHAR(50) NOT NULL,
    supplier_country_code VARCHAR(3) NOT NULL,
    supplier_postal_code VARCHAR(20) NOT NULL,
    primary_email_address VARCHAR(255) NOT NULL,
    email_notes VARCHAR(1000),
    invoices_data JSONB NOT NULL,
    alternate_email_addresses_data JSONB,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    failure_reason VARCHAR(1000),
    processed_at TIMESTAMP,
    account_number VARCHAR(50),
    expiration_date VARCHAR(20),
    created_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM',
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM',
    updated_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add comments for documentation
COMMENT ON TABLE process_payments_request IS 'Stores ProcessPayments requests for audit trail and monitoring. Retry logic handled at infrastructure level.';
COMMENT ON COLUMN process_payments_request.message_id IS 'Unique identifier for request correlation';
COMMENT ON COLUMN process_payments_request.idempotency_key IS 'Unique idempotency key for preventing duplicate processing';
COMMENT ON COLUMN process_payments_request.tenant_id IS 'Tenant identifier for multi-tenant buyer ID lookup';
COMMENT ON COLUMN process_payments_request.action_type IS 'Type of payment action';
COMMENT ON COLUMN process_payments_request.client_id IS 'Client identifier';
COMMENT ON COLUMN process_payments_request.buyer_id IS 'Buyer identifier';
COMMENT ON COLUMN process_payments_request.payment_expiry_date IS 'Payment expiration date';
COMMENT ON COLUMN process_payments_request.account_type IS 'Account type identifier';
COMMENT ON COLUMN process_payments_request.currency_code IS 'ISO currency code';
COMMENT ON COLUMN process_payments_request.payment_gross_amount IS 'Total payment amount';
COMMENT ON COLUMN process_payments_request.payment_type IS 'Type of payment';
COMMENT ON COLUMN process_payments_request.supplier_name IS 'Supplier name';
COMMENT ON COLUMN process_payments_request.supplier_id IS 'Supplier identifier';
COMMENT ON COLUMN process_payments_request.supplier_address_line2 IS 'Supplier address line 2';
COMMENT ON COLUMN process_payments_request.supplier_address_line1 IS 'Supplier address line 1';
COMMENT ON COLUMN process_payments_request.supplier_city IS 'Supplier city';
COMMENT ON COLUMN process_payments_request.supplier_state IS 'Supplier state';
COMMENT ON COLUMN process_payments_request.supplier_country_code IS 'Supplier country code';
COMMENT ON COLUMN process_payments_request.supplier_postal_code IS 'Supplier postal code';
COMMENT ON COLUMN process_payments_request.primary_email_address IS 'Primary email address';
COMMENT ON COLUMN process_payments_request.email_notes IS 'Email notes';
COMMENT ON COLUMN process_payments_request.invoices_data IS 'JSONB representation of invoices data';
COMMENT ON COLUMN process_payments_request.alternate_email_addresses_data IS 'JSONB representation of alternate email addresses';
COMMENT ON COLUMN process_payments_request.status IS 'Processing status';
COMMENT ON COLUMN process_payments_request.failure_reason IS 'Reason for failure if any';
COMMENT ON COLUMN process_payments_request.processed_at IS 'Timestamp when processed';
COMMENT ON COLUMN process_payments_request.account_number IS 'Account number from VISA API response';
COMMENT ON COLUMN process_payments_request.expiration_date IS 'Expiration date from VISA API response';
COMMENT ON COLUMN process_payments_request.created_by IS 'User who created the record';
COMMENT ON COLUMN process_payments_request.created_date IS 'Timestamp when record was created';
COMMENT ON COLUMN process_payments_request.updated_by IS 'User who last updated the record';
COMMENT ON COLUMN process_payments_request.updated_date IS 'Timestamp when record was last updated';

-- Create index for tenant_id for optimal query performance
CREATE INDEX idx_process_payments_request_tenant_id ON process_payments_request(tenant_id);

# Production Environment Configuration
#
# IMPORTANT: This file uses environment variables for security.
# Required environment variables:
# - DB_PASSWORD: Database password (no default)
# - VISA_API_BASE_URL: Visa API base URL (no default)
# - VISA_KEYSTORE_PATH: Path to SSL keystore (no default)
# - VISA_KEYSTORE_PASSWORD: SSL keystore password (no default)
# - VISA_KEY_ALIAS: SSL key alias (no default)
# - VISA_KEY_PASSWORD: SSL key password (no default)
#
spring:
  application:
    name: visa-prod

  # PostgreSQL Database Configuration for Production
  datasource:
    url: ${DB_URL:**********************************************************}
    username: ${DB_USERNAME:visa_prod_user}
    password: ${DB_PASSWORD}
    driver-class-name: org.postgresql.Driver
    # Connection Pool Configuration for Production
    hikari:
      maximum-pool-size: ${DB_POOL_MAX_SIZE:20}
      minimum-idle: ${DB_POOL_MIN_IDLE:10}
      connection-timeout: ${DB_CONNECTION_TIMEOUT:30000}
      idle-timeout: ${DB_IDLE_TIMEOUT:600000}
      max-lifetime: ${DB_MAX_LIFETIME:1800000}
      leak-detection-threshold: ${DB_LEAK_DETECTION:60000}

  # JPA/Hibernate Configuration for Production
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
        use_sql_comments: false
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        # Hibernate Envers Configuration for Audit Trail (Production)
        envers:
          audit_table_suffix: _aud
          revision_field_name: rev
          revision_type_field_name: revtype
          store_data_at_delete: true
          default_schema: ""
          track_entities_changed_in_revision: false

  # Flyway Configuration for Production
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: false
    baseline-version: 0
    validate-on-migrate: true
    clean-disabled: true
    out-of-order: false

# Visa API Configuration for Production
visa:
  api:
    base-url: ${VISA_API_BASE_URL}
    timeout: ${VISA_API_TIMEOUT:30000}
    connection-timeout: ${VISA_API_CONNECTION_TIMEOUT:10000}
    read-timeout: ${VISA_API_READ_TIMEOUT:30000}
    write-timeout: ${VISA_API_WRITE_TIMEOUT:30000}
    user-id: ${VISA_API_USER_ID:}
    password: ${VISA_API_PASSWORD:}
    client-id: ${VISA_API_CLIENT_ID:B2BWS_4_9_4477}
    buyer-id: ${VISA_API_BUYER_ID:041124}
    retry:
      max-attempts: ${VISA_API_MAX_RETRIES:5}
      initial-delay: ${VISA_API_INITIAL_DELAY:2000}
      max-delay: ${VISA_API_MAX_DELAY:30000}
      multiplier: ${VISA_API_MULTIPLIER:2.0}
    endpoint-configs:
      - name: "process-payments"
        path: ${VISA_API_PROCESS_PAYMENTS_PATH:/vpa/v1/payment/ProcessPayments}
        success-status-code: ${VISA_API_PROCESS_PAYMENTS_SUCCESS_CODE:PP001}
        method: ${VISA_API_PROCESS_PAYMENTS_METHOD:POST}
        description: ${VISA_API_PROCESS_PAYMENTS_DESCRIPTION:ProcessPayments API endpoint for production}
      - name: "manage-payment-controls"
        path: ${VISA_API_MANAGE_PAYMENT_CONTROLS_PATH:/vpa/v1/accountManagement/ManagePaymentControls}
        success-status-code: ${VISA_API_MANAGE_PAYMENT_CONTROLS_SUCCESS_CODE:00}
        method: ${VISA_API_MANAGE_PAYMENT_CONTROLS_METHOD:POST}
        description: ${VISA_API_MANAGE_PAYMENT_CONTROLS_DESCRIPTION:ManagePaymentControls API endpoint for production}
      - name: "get-payment-controls"
        path: ${VISA_API_GET_PAYMENT_CONTROLS_PATH:/vpa/v2/accountManagement/getPaymentControls}
        success-status-code: ${VISA_API_GET_PAYMENT_CONTROLS_SUCCESS_CODE:AMGP000}
        method: ${VISA_API_GET_PAYMENT_CONTROLS_METHOD:POST}
        description: ${VISA_API_GET_PAYMENT_CONTROLS_DESCRIPTION:GetPaymentControls API endpoint for production}



  # SSL Configuration for Production (required)
  ssl:
    enabled: true
    keystore:
      path: ${VISA_KEYSTORE_PATH}
      password: ${VISA_KEYSTORE_PASSWORD}
      type: ${VISA_KEYSTORE_TYPE:PKCS12}
    key:
      alias: ${VISA_KEY_ALIAS}
      password: ${VISA_KEY_PASSWORD}
    protocol: TLSv1.2
    enabled-protocols: TLSv1.2
    use-same-keystore-for-trust: true
    verify-hostname: ${VISA_SSL_VERIFY_HOSTNAME:true}

# Web Server Configuration for Production
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: ${CONTEXT_PATH:}

# Management Endpoints for Production
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: never

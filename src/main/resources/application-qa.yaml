# QA Environment Configuration
spring:
  application:
    name: visa-qa

  # PostgreSQL Database Configuration for QA
  datasource:
    url: ********************************************************
    username: visa_qa_user
    password: visa_qa_pass
    driver-class-name: org.postgresql.Driver
    # Connection Pool Configuration for QA
    hikari:
      maximum-pool-size: 15
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # JPA/Hibernate Configuration for QA
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
        use_sql_comments: false
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        # Hibernate Envers Configuration for Audit Trail (QA)
        envers:
          audit_table_suffix: _aud
          revision_field_name: rev
          revision_type_field_name: revtype
          store_data_at_delete: true
          default_schema: ""
          track_entities_changed_in_revision: false

  # Flyway Configuration for QA
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: false
    baseline-version: 0
    validate-on-migrate: true
    clean-disabled: true
    out-of-order: false

# Visa API Configuration for QA
visa:
  api:
    base-url: https://cert.api.visa.com
    timeout: 15000
    connection-timeout: 10000
    read-timeout: 30000
    write-timeout: 30000
    user-id: ""
    password: ""
    client-id: ${VISA_API_CLIENT_ID:B2BWS_4_9_4477}
    buyer-id: ${VISA_API_BUYER_ID:041124}
    retry:
      max-attempts: 3
      initial-delay: 1000
      max-delay: 10000
      multiplier: 2.0
    endpoint-configs:
      - name: "process-payments"
        path: /vpa/v1/payment/ProcessPayments
        success-status-code: PP001
        method: POST
        description: ProcessPayments API endpoint for QA
      - name: "manage-payment-controls"
        path: /vpa/v1/accountManagement/ManagePaymentControls
        success-status-code: "00"
        method: POST
        description: ManagePaymentControls API endpoint for QA
      - name: "get-payment-controls"
        path: /vpa/v2/accountManagement/getPaymentControls
        success-status-code: AMGP000
        method: POST
        description: GetPaymentControls API endpoint for QA



  # SSL Configuration for QA
  ssl:
    enabled: true
    keystore:
      path: ""
      password: ""
      type: PKCS12
    key:
      alias: ""
      password: ""
    protocol: TLSv1.2
    enabled-protocols: TLSv1.2
    use-same-keystore-for-trust: true
    verify-hostname: true

# Web Server Configuration for QA
server:
  port: 8080
  servlet:
    context-path:

# Management Endpoints for QA
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# Development Environment Configuration
spring:
  application:
    name: visa-dev

  # PostgreSQL Database Configuration for Development
  datasource:
    url: *****************************************
    username: postgres
    password: root
    driver-class-name: org.postgresql.Driver
    # Connection Pool Configuration for Development
    hikari:
      maximum-pool-size: 5
      minimum-idle: 2
      connection-timeout: 20000
      idle-timeout: 300000
      max-lifetime: 1200000

  # JPA/Hibernate Configuration for Development - USING FLYWAY FOR SCHEMA MANAGEMENT
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        # Hibernate Envers Configuration for Audit Trail (Development)
        envers:
          audit_table_suffix: _aud
          revision_field_name: rev
          revision_type_field_name: revtype
          store_data_at_delete: true
          default_schema: ""
          track_entities_changed_in_revision: false

  # Flyway Configuration for Development - ENABLED FOR CONSISTENT SCHEMA MANAGEMENT
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    baseline-version: 0
    validate-on-migrate: true
    clean-disabled: false
    out-of-order: false

# Visa API Configuration for Development
visa:
  api:
    base-url: https://cert.api.visa.com
    timeout: 10000
    connection-timeout: 10000
    read-timeout: 30000
    write-timeout: 30000
    user-id: 2P9LVK8TRIQ9I5NAOFW721t5lV_t0weOQTR8Tr4eggzxgeiAU
    password: mv2Euz2ke9ZZ9H302gAml9yD
    client-id: ${VISA_API_CLIENT_ID:B2BWS_4_9_4477}
    buyer-id: ${VISA_API_BUYER_ID:041124}
    retry:
      max-attempts: 3
      initial-delay: 500
      max-delay: 5000
      multiplier: 2.0
    endpoint-configs:
      - name: "process-payments"
        path: /vpa/v1/payment/ProcessPayments
        success-status-code: PP001
        method: POST
        description: ProcessPayments API endpoint for development
      - name: "manage-payment-controls"
        path: /vpa/v1/accountManagement/ManagePaymentControls
        success-status-code: "00"
        method: POST
        description: ManagePaymentControls API endpoint for development
      - name: "get-payment-controls"
        path: /vpa/v2/accountManagement/getPaymentControls
        success-status-code: AMGP000
        method: POST
        description: GetPaymentControls API endpoint for development


  # SSL Configuration for Development
  ssl:
    enabled: true
    keystore:
      path: classpath:certs/api-visa.jks
      password: Vt$5tRl%^Nj!
      type: PKCS12
    key:
      alias: "1"
      password: Vt$5tRl%^Nj!
    protocol: TLSv1.2
    enabled-protocols: TLSv1.2
    use-same-keystore-for-trust: true
    verify-hostname: false

# Web Server Configuration for Development
server:
  port: 8080
  servlet:
    context-path:

# Management Endpoints for Development
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

plugins {
	kotlin("jvm") version "2.1.0"
	kotlin("plugin.spring") version "2.1.0"
	id("org.springframework.boot") version "3.5.0"
	id("io.spring.dependency-management") version "1.1.7"
	kotlin("plugin.jpa") version "2.1.0"
	id("org.openapi.generator") version "7.10.0"
}

group = "com.papertrl"
version = "0.0.1-SNAPSHOT"

java {
	toolchain {
		languageVersion.set(JavaLanguageVersion.of(19))
	}
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
	compilerOptions {
		jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_19)
	}
}

tasks.withType<JavaCompile> {
	targetCompatibility = "19"
	sourceCompatibility = "19"
}


configurations {
	compileOnly {
		extendsFrom(configurations.annotationProcessor.get())
	}
}

repositories {
	mavenCentral()
}

dependencies {
	// Spring Boot Starters
	implementation("org.springframework.boot:spring-boot-starter-data-jpa")
	implementation("org.springframework.boot:spring-boot-starter-web")
	implementation("org.springframework.boot:spring-boot-starter-validation")
	implementation("org.springframework.boot:spring-boot-starter-webflux")

	// OpenAPI and Swagger
	implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.9")
	implementation("io.swagger.core.v3:swagger-annotations:2.2.30")
	implementation("org.openapitools:jackson-databind-nullable:0.2.6")
	implementation("org.springframework.boot:spring-boot-starter-actuator")

	// Security: Force secure version of commons-lang3 to fix CVE-2025-48924
	implementation("org.apache.commons:commons-lang3:3.18.0")

	// Database
	runtimeOnly("org.postgresql:postgresql")
	implementation("org.flywaydb:flyway-core")
	implementation("org.flywaydb:flyway-database-postgresql")

	// Kotlin
	implementation("org.jetbrains.kotlin:kotlin-reflect")
	implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")

	// HTTP Client and SSL
	implementation("io.netty:netty-handler")
	implementation("io.netty:netty-codec-http")
	implementation("io.netty:netty-transport-native-epoll")
	implementation("io.netty:netty-transport-native-kqueue")

	// Lombok (optional, mainly for Java interop)
	compileOnly("org.projectlombok:lombok")
	annotationProcessor("org.projectlombok:lombok")

	// Testing
	testImplementation("org.springframework.boot:spring-boot-starter-test")
	testImplementation("org.springframework.boot:spring-boot-testcontainers")
	testImplementation("org.jetbrains.kotlin:kotlin-test-junit5")
	testImplementation("org.mockito.kotlin:mockito-kotlin:5.4.0")
	testImplementation("com.squareup.okhttp3:mockwebserver:4.12.0")
	testRuntimeOnly("org.junit.platform:junit-platform-launcher")
}

kotlin {
	compilerOptions {
		freeCompilerArgs.addAll("-Xjsr305=strict")
	}
}

allOpen {
	annotation("jakarta.persistence.Entity")
	annotation("jakarta.persistence.MappedSuperclass")
	annotation("jakarta.persistence.Embeddable")
}

tasks.withType<Test> {
	useJUnitPlatform()
}

// OpenAPI Generator Configuration
openApiGenerate {
	generatorName.set("kotlin-spring")
	inputSpec.set("$rootDir/src/main/resources/api/visa-payments-api.yaml")
	outputDir.set("${layout.buildDirectory.get().asFile}/generated")
	apiPackage.set("com.papertrl.visa.infrastructure.incoming.api")
	modelPackage.set("com.papertrl.visa.infrastructure.incoming.dto.generated")
	configOptions.set(mapOf(
		"dateLibrary" to "java8",
		"interfaceOnly" to "true",
		"useTags" to "true",
		"skipDefaultInterface" to "true",
		"useBeanValidation" to "true",
		"performBeanValidation" to "true",
		"useSpringBoot3" to "true",
		"reactive" to "false",
		"serializationLibrary" to "jackson",
		"basePackage" to "com.papertrl.visa.infrastructure.incoming"
	))
}

// Ensure generated sources are compiled
sourceSets {
	main {
		kotlin {
			srcDir("${layout.buildDirectory.get().asFile}/generated/src/main/kotlin")
		}
	}
}

// Post-process generated files to remove example fields
tasks.register("removeExamplesFromGeneratedCode") {
	dependsOn("openApiGenerate")
	doLast {
		val generatedDir = file("${layout.buildDirectory.get().asFile}/generated/src/main/kotlin")
		if (generatedDir.exists()) {
			generatedDir.walkTopDown()
				.filter { it.isFile && it.extension == "kt" }
				.forEach { file ->
					val content = file.readText()
					val updatedContent = content.replace(
						Regex("""example = "null", """),
						""
					)
					if (content != updatedContent) {
						file.writeText(updatedContent)
						println("Removed example fields from: ${file.name}")
					}
				}
		}
	}
}

// Generate API before compilation
tasks.compileKotlin {
	dependsOn("removeExamplesFromGeneratedCode")
}


